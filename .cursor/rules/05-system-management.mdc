---
description: System-Management, Feedback, Standards, E-Mail und Mitarbeiter
globs: **/system/**, **/verwaltung/**, **/feedback/**
alwaysApply: false
---
# System-Management

Zentrale Verwaltung von systemweiten Einstellungen, Feedback und Mitarbeitern.

## Feedback-System

### Workflow
- **Sammlung**: Über Feedback-Button in der Navigation (gelbes Ausrufezeichen)
- **Kategorisierung**: "feature" oder "bug" Requests
- **Status-Tracking**: "offen", "in_bearbeitung", "erledigt"
- **Verwaltung**: Zentrale Verwaltungsseite unter `/system/feedback`

## Standards-System

### Zweck
Zentrale Anzeige von systemweiten Standardeinstellungen für PDF-Dokumente.

### UI-Organisation (Route: `/system/standards`)
- **E-Mail Tab**: Nur allgemeine E-Mail-Konfiguration (TurboSMTP API-Keys)
- **Lieferscheine Tab**:
  - Logo-Anzeige und PDF-Einstellungen
  - E-Mail-Einstellungen für Lieferscheine
  - E-Mail-Template für Lieferscheine
- **Übersichten Tab**:
  - Logo-Anzeige und PDF-Einstellungen
  - E-Mail-Einstellungen für Übersichten
  - E-Mail-Template für Übersichten

### Konfiguration
- **Feature-spezifische Configs**: Daten werden aus `lieferscheineConfig.ts` und `uebersichtenConfig.ts` geladen
- **Logo-Verwaltung**: Upload und Speicherung über Convex Storage

## E-Mail-System

### TurboSMTP-Integration
- **Externe API**: Zuverlässiger Versand über TurboSMTP Gateway
- **PDF-Anhänge**: Clientseitige PDF-Generierung mit React-PDF
- **Empfänger-Management**: Automatische CC-Listen mit Mitarbeitern

### Kritische Implementierungsdetails
```typescript
// Wichtig: Header mit großem 'C'
headers: {
  "Consumerkey": config.consumerKey,     // NICHT "consumerKey"
  "Consumersecret": config.consumerSecret, // NICHT "consumerSecret"
  "Content-Type": "application/json"
}
```

### Anrede-Logik
- **Hauptansprechpartner**: Konsistente Anrede basierend auf Kundenstammdaten
- **Fallback**: Generische Anrede bei fehlendem Hauptansprechpartner

### Logging-System
Kompakte, strukturierte Logs für E-Mail-Versand:

```
[EMAIL] 📧 Lieferschein "LS-2024-001" | User: <EMAIL> | Recipients: Max Mustermann <<EMAIL>>, Anna Schmidt <<EMAIL>>

[EMAIL] 📊 Übersicht "Kunde1 (31.05.2025 - 29.06.2025)" | User: <EMAIL> | Recipients: Sven Labitzki <<EMAIL>>

[EMAIL] ❌ Lieferschein "LS-2024-002" | User: <EMAIL> | Error: E-Mail-Konfiguration unvollständig
```

**Log-Informationen:**
- **Wer**: Clerk User Email (triggering user)
- **Was**: Dokumentnummer/-bezeichnung
- **An wen**: Vollständige Liste aller Empfänger-E-Mail-Adressen
- **Fehler**: Kompakte Fehlermeldung (falls vorhanden)

## Mitarbeiter-Verwaltung

### Zweck
- **E-Mail-Integration**: Automatische CC-Listen für Dokument-Versand
- **Leistungserfassung**: Zuordnung von Mitarbeitern zu Leistungen
- **Interne Verwaltung**: Zentrale Mitarbeiterdatenbank

### API-Struktur
- **Basis**: `api.verwaltung.mitarbeiter.*`
- **Frontend**: `/verwaltung/mitarbeiter/`

### Datenmodell
```typescript
{
  name: string,
  email: string
}
```

## Doku-Kategorien-System

### Konfigurationsbasiert
- **Definition**: Kategorien in `convex/system/dokuKategorienConfig.ts`
- **Synchronisation**: Automatische Datenbank-Updates via `initializeDefaults`
- **Felder-Typen**: Text, Password, URL, Number, etc.
- **Validierung**: Pflichtfelder und Typen-Validierung