/**
 * Configuration file for email system
 * This file contains only the TurboSMTP configuration and environment variables
 * Email templates are stored in the respective feature config files (lieferscheineConfig, uebersichtenConfig)
 */

// TurboSMTP configuration interface
export interface EmailConfig {
	consumerKey: string;
	consumerSecret: string;
	sender: {
		email: string;
		name: string;
	};
	endpoint: string;
}

// Default email configuration (empty, will be filled from environment variables)
export const defaultEmailConfig: EmailConfig = {
	consumerKey: "",
	consumerSecret: "",
	sender: {
		email: "",
		name: "",
	},
	endpoint: "",
};

// Function to get email configuration with environment variables
export function getEmailConfig(): EmailConfig {
	return {
		consumerKey: process.env.TURBOSMTP_CONSUMER_KEY || "",
		consumerSecret: process.env.TURBOSMTP_CONSUMER_SECRET || "",
		sender: {
			email: process.env.EMAIL_FROM || "",
			name: process.env.EMAIL_FROM_NAME || "",
		},
		endpoint: process.env.TURBOSMTP_API_URL || "",
	};
}
