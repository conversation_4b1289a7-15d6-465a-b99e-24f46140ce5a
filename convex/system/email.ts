import { v } from "convex/values";
import { internal } from "../_generated/api";
import { Id } from "../_generated/dataModel";
import { internalAction, mutation } from "../_generated/server";
import { getLieferscheinEmailTemplate } from "../erstellung/lieferscheineConfig";
import { getUebersichtEmailTemplate } from "../erstellung/uebersichtenConfig";
import { getEmailConfig } from "./emailConfig";

/**
 * Send a Lieferschein as an email
 */
export const sendLieferschein = mutation({
	args: {
		lieferscheinId: v.id("kunden_lieferscheine"),
		sendToMitarbeiter: v.boolean(),
		sendToKunde: v.boolean(),
		pdfBase64: v.optional(v.string()),
		mainContactName: v.optional(v.string()),
		employeeRecipients: v.optional(
			v.array(
				v.object({
					email: v.string(),
					name: v.string(),
				}),
			),
		),
		customerRecipients: v.optional(
			v.array(
				v.object({
					email: v.string(),
					name: v.string(),
				}),
			),
		),
	},
	handler: async (ctx, args) => {
		// Authenticate the user
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const {
			lieferscheinId,
			sendToMitarbeiter,
			sendToKunde,
			pdfBase64,
			mainContactName,
			employeeRecipients,
			customerRecipients,
		} = args;

		// We'll log after we have all the information including lieferschein number

		// Get the lieferschein
		const lieferschein = await ctx.db.get(lieferscheinId);
		if (!lieferschein) {
			throw new Error("Lieferschein nicht gefunden.");
		}

		// Check if the lieferschein is finalized
		if (lieferschein.status !== "fertig") {
			throw new Error("Lieferschein ist nicht finalisiert.");
		}

		// Get the customer
		const kunde = await ctx.db.get(lieferschein.kundenId);
		if (!kunde) {
			throw new Error("Kunde nicht gefunden.");
		}

		// Get the Lieferschein leistungen
		const lieferscheinLeistungen = await ctx.db
			.query("kunden_lieferscheine_zuordnung")
			.withIndex("by_lieferschein", (q) =>
				q.eq("lieferscheinId", lieferscheinId),
			)
			.collect();

		// Get unique mitarbeiter IDs from leistungen
		const mitarbeiterIds = new Set<Id<"mitarbeiter">>();
		for (const ll of lieferscheinLeistungen) {
			const leistung = await ctx.db.get(ll.leistungId);
			if (leistung && leistung.mitarbeiterId) {
				mitarbeiterIds.add(leistung.mitarbeiterId);
			}
		}

		// Collect all recipients for logging
		const allRecipients: string[] = [];

		// Send emails to mitarbeiter if requested
		if (
			sendToMitarbeiter &&
			employeeRecipients &&
			employeeRecipients.length > 0
		) {
			for (const employee of employeeRecipients) {
				allRecipients.push(`${employee.name} <${employee.email}>`);
				// Use action to send email
				await ctx.scheduler.runAfter(
					0,
					internal.system.email.sendLieferscheinEmailAction,
					{
						toEmail: employee.email,
						toName: employee.name,
						templateData: {
							nummer: lieferschein.nummer || `Entwurf (${lieferscheinId})`,
							datum: new Date(lieferschein.erstelltAm).toLocaleDateString(
								"de-DE",
							),
							empfaenger: mainContactName || employee.name,
						},
						lieferscheinId,
						pdfBase64,
					},
				);
			}
		}

		// Send emails to kunde ansprechpartner if requested
		if (sendToKunde && customerRecipients && customerRecipients.length > 0) {
			for (const customer of customerRecipients) {
				allRecipients.push(`${customer.name} <${customer.email}>`);
				// Use action to send email
				await ctx.scheduler.runAfter(
					0,
					internal.system.email.sendLieferscheinEmailAction,
					{
						toEmail: customer.email,
						toName: customer.name,
						templateData: {
							nummer: lieferschein.nummer || `Entwurf (${lieferscheinId})`,
							datum: new Date(lieferschein.erstelltAm).toLocaleDateString(
								"de-DE",
							),
							empfaenger: mainContactName || customer.name,
						},
						lieferscheinId,
						pdfBase64,
					},
				);
			}
		}

		// Consolidated log entry
		const lieferscheinNummer =
			lieferschein.nummer || `Entwurf-${lieferscheinId}`;
		const recipientsList =
			allRecipients.length > 0 ? allRecipients.join(", ") : "Keine Empfänger";

		try {
			console.log(
				`[EMAIL] 📧 Lieferschein "${lieferscheinNummer}" | User: ${identity.email} | Recipients: ${recipientsList}`,
			);
			return { success: true };
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : "Unbekannter Fehler";
			console.error(
				`[EMAIL] ❌ Lieferschein "${lieferscheinNummer}" | User: ${identity.email} | Error: ${errorMessage}`,
			);
			throw error;
		}
	},
});

/**
 * Send a Übersicht as an email
 */
export const sendUebersicht = mutation({
	args: {
		kundeId: v.id("kunden"),
		zeitraum: v.string(),
		pdfBase64: v.string(),
		employeeRecipients: v.optional(
			v.array(
				v.object({
					email: v.string(),
					name: v.string(),
				}),
			),
		),
		customerRecipients: v.optional(
			v.array(
				v.object({
					email: v.string(),
					name: v.string(),
				}),
			),
		),
		mainContactName: v.optional(v.string()),
	},
	handler: async (ctx, args) => {
		// Authenticate the user
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const {
			kundeId,
			zeitraum,
			pdfBase64,
			employeeRecipients,
			customerRecipients,
			mainContactName,
		} = args;

		// We'll log after we have all the information including kunde name

		// Get the customer
		const kunde = await ctx.db.get(kundeId);
		if (!kunde) {
			throw new Error("Kunde nicht gefunden.");
		}

		// Collect all recipients for logging
		const allRecipients: string[] = [];

		// Send emails to employee recipients
		if (employeeRecipients && employeeRecipients.length > 0) {
			for (const employee of employeeRecipients) {
				allRecipients.push(`${employee.name} <${employee.email}>`);
				// Use action to send email
				await ctx.scheduler.runAfter(
					0,
					internal.system.email.sendUebersichtEmailAction,
					{
						toEmail: employee.email,
						toName: employee.name,
						templateData: {
							kunde: kunde.name,
							zeitraum,
							empfaenger: mainContactName || employee.name,
						},
						pdfBase64,
					},
				);
			}
		}

		// Send emails to customer recipients
		if (customerRecipients && customerRecipients.length > 0) {
			for (const customer of customerRecipients) {
				allRecipients.push(`${customer.name} <${customer.email}>`);
				// Use action to send email
				await ctx.scheduler.runAfter(
					0,
					internal.system.email.sendUebersichtEmailAction,
					{
						toEmail: customer.email,
						toName: customer.name,
						templateData: {
							kunde: kunde.name,
							zeitraum,
							empfaenger: mainContactName || customer.name,
						},
						pdfBase64,
					},
				);
			}
		}

		// Consolidated log entry
		const recipientsList =
			allRecipients.length > 0 ? allRecipients.join(", ") : "Keine Empfänger";

		try {
			console.log(
				`[EMAIL] 📊 Übersicht "${kunde.name} (${zeitraum})" | User: ${identity.email} | Recipients: ${recipientsList}`,
			);
			return { success: true };
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : "Unbekannter Fehler";
			console.error(
				`[EMAIL] ❌ Übersicht "${kunde.name} (${zeitraum})" | User: ${identity.email} | Error: ${errorMessage}`,
			);
			throw error;
		}
	},
});

/**
 * Action to send Lieferschein email (can use fetch)
 */
export const sendLieferscheinEmailAction = internalAction({
	args: {
		toEmail: v.string(),
		toName: v.string(),
		templateData: v.object({
			nummer: v.string(),
			datum: v.string(),
			empfaenger: v.string(),
		}),
		lieferscheinId: v.id("kunden_lieferscheine"),
		pdfBase64: v.optional(v.string()),
	},
	handler: async (_ctx, args) => {
		const { toEmail, toName, templateData, pdfBase64 } = args;

		return await sendEmailWithAttachment(
			toEmail,
			toName,
			"lieferschein",
			templateData,
			pdfBase64 || null,
		);
	},
});

/**
 * Action to send Übersicht email (can use fetch)
 */
export const sendUebersichtEmailAction = internalAction({
	args: {
		toEmail: v.string(),
		toName: v.string(),
		templateData: v.object({
			kunde: v.string(),
			zeitraum: v.string(),
			empfaenger: v.string(),
		}),
		pdfBase64: v.string(),
	},
	handler: async (_ctx, args) => {
		const { toEmail, toName, templateData, pdfBase64 } = args;

		return await sendEmailWithAttachment(
			toEmail,
			toName,
			"uebersicht",
			templateData,
			pdfBase64,
		);
	},
});

// Helper function to send an email with an attachment
async function sendEmailWithAttachment(
	toEmail: string,
	toName: string,
	templateType: "lieferschein" | "uebersicht",
	templateData: Record<string, string>,
	pdfBase64: string | null = null,
) {
	const config = getEmailConfig();

	// Get the appropriate template based on type
	const template =
		templateType === "lieferschein"
			? getLieferscheinEmailTemplate()
			: getUebersichtEmailTemplate();

	// Check if required configuration is available
	if (!config.consumerKey || !config.consumerSecret) {
		console.error(
			"TurboSMTP Consumer Key oder Consumer Secret fehlt in der Konfiguration",
		);
		throw new Error("E-Mail-Konfiguration unvollständig");
	}

	// Replace placeholders in the template
	let subject = template.subject;
	let body = template.body;

	for (const [key, value] of Object.entries(templateData)) {
		subject = subject.replace(new RegExp(`{{${key}}}`, "g"), value);
		body = body.replace(new RegExp(`{{${key}}}`, "g"), value);
	}

	// Email sending details are now logged in the main functions

	// Prepare the email payload for TurboSMTP API v2
	const emailPayload: any = {
		from: `${config.sender.name} <${config.sender.email}>`,
		to: toEmail,
		subject: subject,
		content: body,
		html_content: body.replace(/\n/g, "<br>"),
	};

	// Add attachment if provided
	if (pdfBase64) {
		emailPayload.attachments = [
			{
				name:
					templateType === "lieferschein"
						? `LS${templateData.nummer}.pdf`
						: `Uebersicht_${templateData.kunde}_${templateData.zeitraum}.pdf`,
				content: pdfBase64,
				content_type: "application/pdf",
			},
		];
	}

	try {
		// Make HTTP request to TurboSMTP API v2 with consumer key/secret headers
		const response = await fetch(config.endpoint, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				Consumerkey: config.consumerKey,
				Consumersecret: config.consumerSecret,
				Accept: "application/json",
			},
			body: JSON.stringify(emailPayload),
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error(`TurboSMTP API Error (${response.status}):`, errorText);
			throw new Error(
				`E-Mail-Versand fehlgeschlagen: ${response.status} ${response.statusText}`,
			);
		}

		const result = await response.json();

		// Success is logged in the main functions

		return { success: true, messageId: result.message_id || result.id };
	} catch (error) {
		// Error details are logged in the main functions
		const errorMessage =
			error instanceof Error ? error.message : "Unbekannter Fehler";
		throw new Error(`E-Mail-Versand fehlgeschlagen: ${errorMessage}`);
	}
}
