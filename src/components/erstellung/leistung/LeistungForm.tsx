import { api } from "@/../convex/_generated/api";
import { Doc, Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import { Input } from "@/components/_shared/Input";
import { Label } from "@/components/_shared/Label";
import { ModalFormDialog } from "@/components/_shared/ModalFormDialog";
import { Textarea } from "@/components/_shared/Textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/_shared/select";
import {
	calculateHours,
	formatCurrency,
	formatDate,
	formatHours,
	formatTime,
	toInputDate,
	toInputTime,
} from "@/lib/utils/formatUtils";
// Ensure Doc and Id are imported correctly if KundeDoc changes due to schema update.
// For now, assuming Doc<"kunden"> is still valid for fetching the customer for defaults.
import { useMutation, useQuery } from "convex/react";
import {
	CalendarDays,
	Check,
	Clock,
	Euro,
	Pencil,
	PlusCircle,
	TimerIcon,
	X,
} from "lucide-react";
import React, { useState, useEffect, useMemo } from "react";
import { toast } from "sonner";

// Interfaces
interface LeistungMitNamen {
	// Nur benötigte Felder für Edit
	_id: Id<"kunden_leistungen">;
	kundenId: Id<"kunden">;
	mitarbeiterId: Id<"mitarbeiter">;
	kontingentId: Id<"kunden_kontingente">;
	startZeit: number;
	endZeit: number;
	art: string;
	beschreibung: string;
	stundenpreis: number;
	anfahrtskosten: number;
}
interface KontingentOption {
	_id: Id<"kunden_kontingente">;
	name: string;
	startDatum: number;
	stunden: number;
	verbrauchteStunden: number;
}
type KundeDoc = Doc<"kunden">;
type MitarbeiterDoc = Doc<"mitarbeiter">;

// Use a type for the form state shape
type LeistungFormData = {
	kundeId: string;
	mitarbeiterId: string;
	kontingentId: string;
	datum: string;
	startUhrzeit: string;
	endUhrzeit: string;
	art: "remote" | "vor-Ort" | "vor-Ort (free)";
	stundenpreisInput: string;
	anfahrtskostenInput: string;
	beschreibung: string;
};

// Let the parent calculate initial data
type EditingLeistungData = Omit<
	LeistungFormData,
	"stundenpreisInput" | "anfahrtskostenInput"
> & {
	_id: Id<"kunden_leistungen">;
	stundenpreis: number;
	anfahrtskosten: number;
};

interface LeistungFormProps {
	// editingLeistung: LeistungMitNamen | null; // Replaced by initialData
	initialData: LeistungFormData & { _id?: Id<"kunden_leistungen"> }; // Combined type for new/edit
	isEditing: boolean; // Explicitly pass if we are editing
	kunden: KundeDoc[];
	mitarbeiter: MitarbeiterDoc[];
	onSubmitSuccess: () => void;
	onCancel: () => void;
}

export function LeistungForm({
	initialData,
	isEditing,
	kunden,
	mitarbeiter,
	onSubmitSuccess,
	onCancel,
}: LeistungFormProps) {
	const createLeistung = useMutation(api.erstellung.leistung.create);
	const updateLeistung = useMutation(api.erstellung.leistung.update);

	// Initialize state directly from the prop
	const [formState, setFormState] = useState<LeistungFormData>(initialData);

	// Fetch active contingents based on selected customer IN THE FORM
	const aktiveKontingente = useQuery(
		api.verwaltung.kontingente.getActiveByKunde,
		formState.kundeId
			? { kundenId: formState.kundeId as Id<"kunden"> }
			: "skip",
	) as KontingentOption[] | undefined;

	// Effect to update prices and reset kontingent when customer changes manually in the form
	// This should now correctly ignore the initial population via props
	useEffect(() => {
		// Guard: Don't run if kundeId hasn't been set yet (initial state for new)
		if (!formState.kundeId) {
			return;
		}
		// Guard: Don't run if the initialData still matches the current formState's kundeId
		// This prevents running when the component mounts with initial edit data.
		if (formState.kundeId === initialData.kundeId && formState.kundeId !== "") {
			// If it's the initial kundeId from props (and not empty), skip the price update/kontingent reset here.
			// It means the user hasn't manually changed it yet.
			return;
		}

		// Reset Kontingent
		handleSelectChange("kontingentId", "");

		// Update prices based on the newly selected Kunde
		const selectedKunde = kunden.find((k) => k._id === formState.kundeId);
		setFormState((prev) => ({
			...prev,
			stundenpreisInput: selectedKunde
				? selectedKunde.stundenpreis.toString()
				: "",
			anfahrtskostenInput: selectedKunde
				? selectedKunde.anfahrtskosten.toString()
				: "",
		}));
	}, [formState.kundeId, kunden, initialData.kundeId]); // Depend on form's kundeId and initial kundeId

	// Effect to initialize form state removed - done via prop now

	const handleInputChange = (
		e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
	) => {
		const { id, value } = e.target;
		setFormState((prev) => ({ ...prev, [id]: value }));
	};

	const handleSelectChange = (id: keyof LeistungFormData, value: string) => {
		setFormState((prev) => ({ ...prev, [id]: value }));
	};

	// Helper to convert form times back to timestamps for submission
	const getTimestamps = () => {
		try {
			const [year, month, day] = formState.datum.split("-").map(Number);
			const [startHour, startMinute] = formState.startUhrzeit
				.split(":")
				.map(Number);
			const [endHour, endMinute] = formState.endUhrzeit.split(":").map(Number);

			// Erstelle UTC-Timestamps für deutsche Lokalzeit
			// Juni 2025 = Sommerzeit (+02:00), sonst Winterzeit (+01:00)
			const isDST = (m: number) => m >= 3 && m <= 10; // Grobe Sommerzeit-Bestimmung
			const offsetHours = isDST(month) ? 2 : 1;

			const startDate = new Date(
				Date.UTC(year, month - 1, day, startHour - offsetHours, startMinute, 0),
			);
			let endDate = new Date(
				Date.UTC(year, month - 1, day, endHour - offsetHours, endMinute, 0),
			);

			if (endDate <= startDate) {
				endDate = new Date(
					Date.UTC(
						year,
						month - 1,
						day + 1,
						endHour - offsetHours,
						endMinute,
						0,
					),
				);
			}

			return { startZeit: startDate.getTime(), endZeit: endDate.getTime() };
		} catch (e) {
			toast.error("Ungültiges Datum oder Uhrzeitformat.");
			return null;
		}
	};

	const summaryData = useMemo(() => {
		const timestamps = getTimestamps();
		if (!timestamps)
			return { stunden: 0, kostenStunden: 0, anfahrt: 0, gesamt: 0 }; // Default on error

		const { startZeit, endZeit } = timestamps;
		const stunden = calculateHours(startZeit, endZeit);
		const preis = Number.parseFloat(formState.stundenpreisInput) || 0;
		const anfahrt =
			formState.art === "vor-Ort"
				? Number.parseFloat(formState.anfahrtskostenInput) || 0
				: 0;
		const kostenStunden = stunden * preis;
		const gesamt = kostenStunden + anfahrt;
		return { stunden, kostenStunden, anfahrt, gesamt };
	}, [
		formState.startUhrzeit,
		formState.endUhrzeit,
		formState.datum,
		formState.stundenpreisInput,
		formState.anfahrtskostenInput,
		formState.art,
	]);

	const handleSubmit = () => {
		const timestamps = getTimestamps();
		if (!timestamps) return; // Stop if timestamps failed

		const { startZeit, endZeit } = timestamps;
		const {
			kundeId,
			mitarbeiterId,
			kontingentId,
			art,
			beschreibung,
			stundenpreisInput,
			anfahrtskostenInput,
		} = formState;

		const preis = stundenpreisInput
			? Number.parseFloat(stundenpreisInput)
			: undefined;
		const anfahrt = anfahrtskostenInput
			? Number.parseFloat(anfahrtskostenInput)
			: undefined;

		// Basic validation already done in getTimestamps for date/time
		if (!kundeId || !mitarbeiterId || !kontingentId || !beschreibung) {
			toast.error(
				"Bitte alle Felder (Kunde, Mitarbeiter, Kontingent, Beschreibung) ausfüllen.",
			);
			return;
		}
		if (
			preis === undefined ||
			Number.isNaN(preis) ||
			(art === "vor-Ort" && (anfahrt === undefined || Number.isNaN(anfahrt)))
		) {
			toast.error("Bitte gültige Preise eingeben.");
			return;
		}

		const mutationArgs = {
			kundenId: kundeId as Id<"kunden">,
			mitarbeiterId: mitarbeiterId as Id<"mitarbeiter">,
			kontingentId: kontingentId as Id<"kunden_kontingente">,
			startZeit,
			endZeit,
			beschreibung,
			art,
			stundenpreis: preis,
			anfahrtskosten: art === "vor-Ort" ? anfahrt : 0, // Ensure anfahrt is 0 if not vor-Ort
		};

		const promise =
			isEditing && initialData._id
				? updateLeistung({ id: initialData._id, ...mutationArgs })
				: createLeistung(mutationArgs);

		promise
			.then(() => {
				toast.success(
					`Leistung erfolgreich ${isEditing ? "aktualisiert" : "erfasst"}`,
				);
				onSubmitSuccess();
			})
			.catch((error: Error) => {
				toast.error(
					`Fehler beim Speichern: ${error.message || "Unbekannter Fehler"}`,
				);
			});
	};

	return (
		<ModalFormDialog
			isOpen={true}
			onClose={onCancel}
			title={isEditing ? "Leistung bearbeiten" : "Neue Leistung erfassen"}
			icon={
				isEditing ? (
					<Pencil className="h-3.5 w-3.5" />
				) : (
					<PlusCircle className="h-3.5 w-3.5" />
				)
			}
			footerAction={{
				label: isEditing ? "Änderungen speichern" : "Leistung speichern",
				onClick: handleSubmit,
				icon: <Check className="h-4 w-4" />,
			}}
			maxWidth="2xl"
		>
			<div className="space-y-3">
				{/* Row 1: Kunde, Kontingent, Mitarbeiter */}
				<div className="grid grid-cols-1 md:grid-cols-3 gap-2">
					<div className="space-y-1">
						<Label htmlFor="kundeId">Kunde</Label>
						<Select
							value={formState.kundeId}
							onValueChange={(value) => handleSelectChange("kundeId", value)}
							required
						>
							<SelectTrigger id="kundeId" className="h-9">
								<SelectValue placeholder="Kunde auswählen..." />
							</SelectTrigger>
							<SelectContent>
								{kunden.map((k) => (
									<SelectItem key={k._id} value={k._id}>
										{k.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
					<div className="space-y-1">
						<Label htmlFor="kontingentId">Kontingent</Label>
						<Select
							value={formState.kontingentId}
							onValueChange={(value) =>
								handleSelectChange("kontingentId", value)
							}
							required
							disabled={!formState.kundeId || !aktiveKontingente}
						>
							<SelectTrigger id="kontingentId" className="h-9">
								<SelectValue
									placeholder={
										formState.kundeId
											? aktiveKontingente && aktiveKontingente.length > 0
												? "Kontingent auswählen..."
												: "Keine aktiven Kontingente"
											: "Bitte zuerst Kunde wählen"
									}
								/>
							</SelectTrigger>
							<SelectContent>
								{aktiveKontingente?.map((k) => (
									<SelectItem key={k._id} value={k._id}>
										{k.name}{" "}
										{`(${formatHours(k.stunden - k.verbrauchteStunden)} Rest)`}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
					<div className="space-y-1">
						<Label htmlFor="mitarbeiterId">Mitarbeiter</Label>
						<Select
							value={formState.mitarbeiterId}
							onValueChange={(value) =>
								handleSelectChange("mitarbeiterId", value)
							}
							required
						>
							<SelectTrigger id="mitarbeiterId" className="h-9">
								<SelectValue placeholder="Mitarbeiter auswählen..." />
							</SelectTrigger>
							<SelectContent>
								{mitarbeiter.map((m) => (
									<SelectItem key={m._id} value={m._id}>
										{m.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
				</div>

				{/* Row 2: Datum, Start, Ende, Art */}
				<div className="grid grid-cols-1 md:grid-cols-4 gap-2">
					<div className="space-y-1">
						<Label htmlFor="datum">Datum</Label>
						<div className="relative">
							<CalendarDays className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
							<Input
								id="datum"
								type="date"
								value={formState.datum}
								onChange={handleInputChange}
								required
								className="h-9 pl-8"
							/>
						</div>
					</div>
					<div className="space-y-1">
						<Label htmlFor="startUhrzeit">Start</Label>
						<div className="relative">
							<Clock className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
							<Input
								id="startUhrzeit"
								type="time"
								value={formState.startUhrzeit}
								onChange={handleInputChange}
								required
								step="900"
								className="h-9 pl-8"
							/>
						</div>
					</div>
					<div className="space-y-1">
						<Label htmlFor="endUhrzeit">Ende</Label>
						<div className="relative">
							<Clock className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
							<Input
								id="endUhrzeit"
								type="time"
								value={formState.endUhrzeit}
								onChange={handleInputChange}
								required
								step="900"
								className="h-9 pl-8"
							/>
						</div>
					</div>
					<div className="space-y-1">
						<Label htmlFor="art">Art</Label>
						<Select
							value={formState.art}
							onValueChange={(value) => handleSelectChange("art", value)}
							required
						>
							<SelectTrigger id="art" className="h-9">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="remote">Remote</SelectItem>
								<SelectItem value="vor-Ort">Vor-Ort</SelectItem>
								<SelectItem value="vor-Ort (free)">
									Vor-Ort (kostenlos)
								</SelectItem>
							</SelectContent>
						</Select>
					</div>
				</div>

				{/* Row 3: Beschreibung */}
				<div>
					<Label htmlFor="beschreibung">Beschreibung</Label>
					<Textarea
						id="beschreibung"
						placeholder="Tätigkeitsbeschreibung..."
						value={formState.beschreibung}
						onChange={handleInputChange}
						required
						className="min-h-[140px]"
					/>
				</div>

				{/* Row 4: Summary & Actions */}
				<div className="flex flex-wrap justify-between items-center gap-x-4 gap-y-2 pt-3 border-t border-gray-700/50 mt-3">
					<div className="flex flex-wrap items-center gap-x-3 gap-y-2 text-sm">
						{/* Preiseingabefelder zuerst */}
						<div className="flex items-center gap-1">
							<Label
								htmlFor="stundenpreisInput"
								className="text-xs text-gray-400 whitespace-nowrap"
							>
								Preis/Std:
							</Label>
							<div className="relative w-24">
								<Euro className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-400" />
								<Input
									id="stundenpreisInput"
									type="number"
									step="0.01"
									placeholder="Preis"
									title="Stundenpreis"
									value={formState.stundenpreisInput}
									onChange={handleInputChange}
									className="h-9 pl-7 text-sm"
								/>
							</div>
						</div>
						<div
							className={`flex items-center gap-1 ${formState.art !== "vor-Ort" ? "opacity-50" : ""}`}
						>
							<Label
								htmlFor="anfahrtskostenInput"
								className="text-xs text-gray-400 whitespace-nowrap"
							>
								Anfahrt:
							</Label>
							<div className="relative w-24">
								<Euro className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-400" />
								<Input
									id="anfahrtskostenInput"
									type="number"
									step="0.01"
									placeholder="Preis"
									title="Anfahrtskosten"
									value={formState.anfahrtskostenInput}
									onChange={handleInputChange}
									disabled={formState.art !== "vor-Ort"}
									className="h-9 pl-7 text-sm"
								/>
							</div>
						</div>

						{/* Dann Stunden und Gesamtkosten */}
						<div className="flex items-center gap-1">
							<TimerIcon className="h-4 w-4 text-gray-400" />
							<span>{formatHours(summaryData.stunden)}</span>
						</div>
						<div className="flex items-center gap-1">
							<Euro className="h-4 w-4 text-gray-400" />
							<span className="font-medium">
								{formatCurrency(summaryData.gesamt)}
							</span>
							<span className="text-xs text-gray-400">(Gesamt)</span>
						</div>
					</div>
				</div>
			</div>
		</ModalFormDialog>
	);
}
