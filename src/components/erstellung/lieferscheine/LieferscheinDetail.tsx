import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/_shared/Card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import { PageLayout } from "@/components/layout/PageLayout";
import { EmailButton } from "@/components/system/email";
import { formatCurrency, formatHours } from "@/lib/utils/formatUtils";
import { PDFDownloadLink, PDFViewer } from "@react-pdf/renderer";
import { useMutation, useQuery } from "convex/react";
import {
	AlertTriangle,
	ArrowLeft,
	Download,
	Eye,
	FileText,
	Mail,
	Plus,
	Trash2,
} from "lucide-react";
import { useEffect, useState } from "react";
import { Link, useLocation, useParams } from "react-router-dom";
import { toast } from "sonner";
import { defaultLieferscheinConfig } from "../../../../convex/erstellung/lieferscheineConfig";
import { AddLeistungDialog } from "./AddLeistungDialog";
import { CreateCorrectionDialog } from "./CreateCorrectionDialog";
import { FinalizeLieferscheinDialog } from "./FinalizeLieferscheinDialog";
import { PDFLieferscheinDocument } from "./PDFDocument";

export function LieferscheinDetailPage() {
	const { id } = useParams<{ id: string }>();
	const location = useLocation();
	const [lieferscheinId, setLieferscheinId] =
		useState<Id<"kunden_lieferscheine"> | null>(null);

	const [isAddLeistungDialogOpen, setIsAddLeistungDialogOpen] = useState(false);
	const [isCorrectionDialogOpen, setIsCorrectionDialogOpen] = useState(
		location.state?.openCorrectionDialog || false,
	);
	const [isFinalizationDialogOpen, setIsFinalizationDialogOpen] =
		useState(false);
	const [showPreview, setShowPreview] = useState(false);
	const [bemerkung, setBemerkung] = useState<string | undefined>();

	// PDF settings
	const lieferscheinSettings = defaultLieferscheinConfig.settings;
	const [includeHeaderInPDF, setIncludeHeaderInPDF] = useState(
		lieferscheinSettings.includeHeader,
	);
	const [includeFooterInPDF, setIncludeFooterInPDF] = useState(
		lieferscheinSettings.includeFooter,
	);
	const [showLogoInPDF, setShowLogoInPDF] = useState(
		lieferscheinSettings.showLogo,
	);
	const [processedLogoUrl, setProcessedLogoUrl] = useState<string | undefined>(
		undefined,
	);
	const [includeLegalTextInPDF, setIncludeLegalTextInPDF] = useState(true);
	const [includeSignatureFieldInPDF, setIncludeSignatureFieldInPDF] = useState(
		lieferscheinSettings.includeSignatureField,
	);

	// Load logo from the path in config
	useEffect(() => {
		// Get the logo path from the config
		const logoPath = lieferscheinSettings.logoPath;

		try {
			// Set the logo URL from configuration
			setProcessedLogoUrl(logoPath);

			// Validate logo URL by attempting to load it
			const img = new Image();
			img.onerror = () => {
				// Fallback: disable logo if loading fails
				setProcessedLogoUrl(undefined);
			};
			img.src = logoPath;
		} catch (error) {
			setProcessedLogoUrl(undefined);
		}
	}, []);

	// Check if the id is a Convex ID or a lieferschein number
	const isConvexId = id?.startsWith("k") || id?.startsWith("q");

	const isUndefined = id === "undefined";

	const lieferscheinDataById = useQuery(
		api.erstellung.lieferschein.get,
		isConvexId && !isUndefined
			? { id: id as Id<"kunden_lieferscheine"> }
			: "skip",
	);

	const lieferscheinDataByNummer = useQuery(
		api.erstellung.lieferschein.getByNummer,
		!isConvexId ? { nummer: id || "" } : "skip",
	);

	const lieferscheinData = isConvexId
		? lieferscheinDataById
		: lieferscheinDataByNummer;

	// Update lieferscheinId when data is loaded
	useEffect(() => {
		if (lieferscheinData && !lieferscheinId) {
			setLieferscheinId(lieferscheinData.lieferschein._id);
		}
	}, [lieferscheinData, lieferscheinId]);

	// Update bemerkung when lieferschein data is loaded
	useEffect(() => {
		if (lieferscheinData?.lieferschein) {
			setBemerkung(lieferscheinData.lieferschein.bemerkung);
		}
	}, [lieferscheinData]);

	const removeLeistung = useMutation(
		api.erstellung.lieferschein.removeLeistung,
	);

	const updateLieferschein = useMutation(api.erstellung.lieferschein.update);

	// Bemerkung aktualisieren
	const handleUpdateBemerkung = async () => {
		if (!lieferscheinId) {
			toast.error("Lieferschein-ID nicht verfügbar.");
			return;
		}

		try {
			await updateLieferschein({
				id: lieferscheinId,
				bemerkung: bemerkung || undefined,
			});
			toast.success("Bemerkung erfolgreich aktualisiert.");
		} catch (error) {
			console.error("Fehler beim Aktualisieren der Bemerkung:", error);
			toast.error("Fehler beim Aktualisieren der Bemerkung.");
		}
	};

	if (!lieferscheinData) {
		return (
			<PageLayout
				title="Lieferschein wird geladen..."
				subtitle="Bitte warten Sie einen Moment"
				backLink="/erstellung/lieferscheine"
				backLinkText="Zurück zur Übersicht"
			>
				<div className="flex justify-center items-center h-64">
					<div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500" />
				</div>
			</PageLayout>
		);
	}

	const { lieferschein, leistungen, korrekturen, original } = lieferscheinData;

	const handleRemoveLeistung = async (leistungId: Id<"kunden_leistungen">) => {
		if (!lieferscheinId) {
			toast.error("Lieferschein-ID nicht verfügbar.");
			return;
		}

		try {
			await removeLeistung({
				lieferscheinId,
				leistungId,
			});
			toast.success("Leistung erfolgreich vom Lieferschein entfernt.");
		} catch (error) {
			toast.error("Fehler beim Entfernen der Leistung.");
		}
	};

	// Calculate total
	const total = leistungen.reduce(
		(sum, leistung) =>
			sum +
			leistung.stunden * leistung.stundenpreis +
			(leistung.mitAnfahrt ? leistung.anfahrtskosten : 0),
		0,
	);

	// Create PDF document instance
	const pdfKey = `pdf-${lieferschein.nummer}-${leistungen.length}-${includeHeaderInPDF}-${includeFooterInPDF}-${showLogoInPDF}-${includeLegalTextInPDF}-${includeSignatureFieldInPDF}-${Date.now()}`;

	const documentInstance = (
		<PDFLieferscheinDocument
			key={pdfKey}
			lieferschein={lieferschein}
			leistungen={leistungen}
			includeHeader={includeHeaderInPDF}
			includeFooter={includeFooterInPDF}
			showLogo={showLogoInPDF}
			includeLegalText={includeLegalTextInPDF}
			includeSignatureField={includeSignatureFieldInPDF}
			logoUrl={processedLogoUrl}
			firmenName="innov8-IT"
			firmenFusszeileText={lieferscheinSettings.fusszeileText}
			legalText={lieferscheinSettings.legalText}
			signatureText={lieferscheinSettings.signatureText}
			formatCurrency={formatCurrency}
			formatHours={formatHours}
		/>
	);

	const pdfFileName = `ls${lieferschein.nummer || lieferschein._id}.pdf`;

	return (
		<PageLayout
			title={`Lieferschein: ${lieferschein.nummer}`}
			subtitle={`Kunde: ${lieferschein.kundeName} | Erstellt am: ${lieferschein.erstelltAmFormatiert}`}
			backLink="/erstellung/lieferscheine"
			backLinkText="Zurück zur Übersicht"
		>
			<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
				{/* Left Column: Controls and Options */}
				<div className="lg:col-span-1 space-y-6">
					{/* Status Card */}
					<Card className="shadow-lg border-0">
						<CardHeader className="p-4">
							<CardTitle className="text-base font-medium">Status</CardTitle>
						</CardHeader>
						<CardContent className="p-4 pt-0">
							<div className="space-y-4">
								{lieferschein.status === "entwurf" ? (
									<div className="flex items-center text-blue-400">
										<FileText className="h-5 w-5 mr-2" />
										<span>
											{lieferschein.istKorrektur
												? "Korrektur-Entwurf"
												: "Lieferschein-Entwurf"}
										</span>
									</div>
								) : lieferschein.istKorrektur ? (
									<div className="flex items-center text-orange-400">
										<AlertTriangle className="h-5 w-5 mr-2" />
										<span>
											Dies ist eine Korrektur zum Lieferschein{" "}
											{original?.nummer || ""}
										</span>
									</div>
								) : lieferschein.hatKorrektur ? (
									<div className="flex items-center text-orange-400">
										<AlertTriangle className="h-5 w-5 mr-2" />
										<span>
											Zu diesem Lieferschein existieren Korrekturen. Die
											aktuellste Version sollte verwendet werden.
										</span>
									</div>
								) : (
									<div className="flex items-center text-green-400">
										<FileText className="h-5 w-5 mr-2" />
										<span>Aktiver Lieferschein</span>
									</div>
								)}

								<div className="mt-4">
									<h3 className="text-sm font-medium mb-1">Bemerkung:</h3>
									{lieferschein.status === "entwurf" ? (
										<>
											<textarea
												className="w-full p-2 border rounded-md text-sm bg-gray-800 text-white"
												value={bemerkung || ""}
												onChange={(e) => setBemerkung(e.target.value)}
												placeholder="Bemerkung hinzufügen (optional)"
												rows={3}
											/>
											{bemerkung !== lieferschein.bemerkung && !showPreview && (
												<div className="mt-2 flex justify-end">
													<Button
														size="sm"
														onClick={handleUpdateBemerkung}
														className="gap-1.5"
													>
														Bemerkung speichern
													</Button>
												</div>
											)}
										</>
									) : lieferschein.bemerkung ? (
										<p className="text-sm text-gray-400">
											{lieferschein.bemerkung}
										</p>
									) : (
										<p className="text-sm text-gray-400 italic">
											Keine Bemerkung vorhanden
										</p>
									)}
								</div>

								<div className="flex flex-wrap gap-2 mt-4">
									{lieferschein.status === "entwurf" ? (
										<Button
											onClick={() => setIsFinalizationDialogOpen(true)}
											className="h-10 w-10 p-0 bg-green-600 hover:bg-green-700"
											title={
												lieferschein.istKorrektur
													? "Korrektur finalisieren"
													: "Lieferschein finalisieren"
											}
										>
											<FileText className="h-5 w-5" />
										</Button>
									) : (
										<PDFDownloadLink
											key={`download-${pdfKey}`}
											document={documentInstance}
											fileName={pdfFileName}
										>
											{({ loading }) => (
												<Button
													disabled={loading}
													className="h-10 w-10 p-0"
													title="PDF herunterladen"
												>
													<Download className="h-5 w-5" />
												</Button>
											)}
										</PDFDownloadLink>
									)}

									<Button
										variant="outline"
										onClick={() => setShowPreview(!showPreview)}
										className="h-10 w-10 p-0"
										title={
											showPreview ? "Vorschau ausblenden" : "Vorschau anzeigen"
										}
									>
										<Eye className="h-5 w-5" />
									</Button>

									{lieferschein.status === "fertig" && lieferscheinId && (
										<EmailButton
											type="lieferschein"
											lieferscheinId={lieferscheinId}
											kundeId={lieferschein.kundenId}
											variant="outline"
											className="h-10 w-10 p-0"
											title="E-Mail senden"
										>
											<Mail className="h-5 w-5" />
										</EmailButton>
									)}

									{!lieferschein.istKorrektur &&
										lieferschein.status === "fertig" && (
											<Button
												variant="outline"
												onClick={() => setIsCorrectionDialogOpen(true)}
												className="h-10 w-10 p-0"
												title="Korrektur erstellen"
											>
												<AlertTriangle className="h-5 w-5" />
											</Button>
										)}
								</div>
							</div>
						</CardContent>
					</Card>

					{/* Related Documents Card */}
					{(korrekturen.length > 0 || original) && (
						<Card className="shadow-lg border-0">
							<CardHeader className="p-4">
								<CardTitle className="text-base font-medium">
									Zugehörige Dokumente
								</CardTitle>
							</CardHeader>
							<CardContent className="p-4 pt-0">
								<div className="space-y-4">
									{original && (
										<div>
											<h4 className="text-sm font-medium mb-2">
												Original-Lieferschein:
											</h4>
											<Link
												to={`/erstellung/lieferscheine/${original.nummer}`}
												className="flex items-center text-blue-400 hover:text-blue-300"
											>
												<FileText className="h-4 w-4 mr-1" />
												{original.nummer} vom {original.erstelltAmFormatiert}
											</Link>
										</div>
									)}

									{korrekturen.length > 0 && (
										<div>
											<h4 className="text-sm font-medium mb-2">Korrekturen:</h4>
											<ul className="space-y-2">
												{korrekturen.map((korrektur) => (
													<li key={korrektur._id}>
														<Link
															to={`/erstellung/lieferscheine/${korrektur.nummer}`}
															className="flex items-center text-blue-400 hover:text-blue-300"
														>
															<FileText className="h-4 w-4 mr-1" />
															{korrektur.nummer} vom{" "}
															{korrektur.erstelltAmFormatiert}
														</Link>
													</li>
												))}
											</ul>
										</div>
									)}
								</div>
							</CardContent>
						</Card>
					)}

					{/* PDF Settings Card */}
					<Card className="shadow-lg border-0">
						<CardHeader className="p-4">
							<CardTitle className="text-base font-medium">
								PDF-Einstellungen
							</CardTitle>
						</CardHeader>
						<CardContent className="p-4 pt-0">
							<div className="space-y-3">
								<div className="flex items-center justify-between">
									<label htmlFor="includeHeader" className="text-sm">
										Header anzeigen
									</label>
									<input
										type="checkbox"
										id="includeHeader"
										checked={includeHeaderInPDF}
										onChange={(e) => setIncludeHeaderInPDF(e.target.checked)}
										className="h-4 w-4"
									/>
								</div>
								<div className="flex items-center justify-between">
									<label
										htmlFor="showLogo"
										className={`text-sm ${!processedLogoUrl ? "opacity-50" : ""}`}
									>
										Logo anzeigen
										{!processedLogoUrl &&
											" (Logo wird aus der Konfiguration geladen)"}
									</label>
									<input
										type="checkbox"
										id="showLogo"
										checked={showLogoInPDF}
										onChange={(e) => setShowLogoInPDF(e.target.checked)}
										disabled={!processedLogoUrl}
										className="h-4 w-4"
									/>
								</div>
								<div className="flex items-center justify-between">
									<label htmlFor="includeFooter" className="text-sm">
										Footer anzeigen
									</label>
									<input
										type="checkbox"
										id="includeFooter"
										checked={includeFooterInPDF}
										onChange={(e) => setIncludeFooterInPDF(e.target.checked)}
										className="h-4 w-4"
									/>
								</div>
								<div className="flex items-center justify-between">
									<label htmlFor="includeLegalText" className="text-sm">
										Rechtlichen Hinweis anzeigen
									</label>
									<input
										type="checkbox"
										id="includeLegalText"
										checked={includeLegalTextInPDF}
										onChange={(e) => setIncludeLegalTextInPDF(e.target.checked)}
										className="h-4 w-4"
									/>
								</div>
								<div className="flex items-center justify-between">
									<label htmlFor="includeSignatureField" className="text-sm">
										Unterschriftsfeld anzeigen
									</label>
									<input
										type="checkbox"
										id="includeSignatureField"
										checked={includeSignatureFieldInPDF}
										onChange={(e) =>
											setIncludeSignatureFieldInPDF(e.target.checked)
										}
										className="h-4 w-4"
									/>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>

				{/* Right Column: PDF Preview or Leistungen */}
				<div className="lg:col-span-2">
					{showPreview ? (
						<Card className="shadow-lg border-0 h-[calc(100vh-12rem)]">
							{typeof window !== "undefined" && (
								<PDFViewer
									key={pdfKey}
									width="100%"
									height="100%"
									className="rounded-md"
									showToolbar={true}
								>
									{documentInstance}
								</PDFViewer>
							)}
						</Card>
					) : (
						<Card className="shadow-lg border-0">
							<CardHeader className="p-4 flex flex-row items-center justify-between">
								<div>
									<CardTitle className="text-base font-medium">
										Enthaltene Leistungen
									</CardTitle>
									<CardDescription>
										{leistungen.length} Leistung(en) auf diesem Lieferschein
									</CardDescription>
								</div>
								{lieferschein.status === "entwurf" && (
									<Button
										size="sm"
										onClick={() => setIsAddLeistungDialogOpen(true)}
										className="h-10 w-10 p-0"
										title="Leistung hinzufügen"
									>
										<Plus className="h-5 w-5" />
									</Button>
								)}
							</CardHeader>
							<CardContent className="p-0">
								<div className="overflow-x-auto">
									<Table>
										<TableHeader>
											<TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
												<TableHead className="font-medium">
													Datum / Kontingent
												</TableHead>
												<TableHead className="font-medium">
													Zeit / Art
												</TableHead>
												<TableHead className="font-medium">
													Mitarbeiter / Std.
												</TableHead>
												<TableHead className="font-medium">
													Beschreibung
												</TableHead>
												{lieferschein.status === "entwurf" && (
													<TableHead className="w-16"></TableHead>
												)}
											</TableRow>
										</TableHeader>
										<TableBody>
											{leistungen.length === 0 ? (
												<TableRow>
													<TableCell
														colSpan={lieferschein.status === "entwurf" ? 6 : 5}
														className="text-center py-8 text-gray-400"
													>
														Keine Leistungen auf diesem Lieferschein.
													</TableCell>
												</TableRow>
											) : (
												<>
													{leistungen.map((leistung) => (
														<TableRow
															key={leistung._id}
															className="border-b border-gray-800"
														>
															<TableCell>
																{leistung.datum}
																{leistung.kontingentName && (
																	<div className="text-xs text-gray-400">
																		{leistung.kontingentName}
																	</div>
																)}
															</TableCell>
															<TableCell>
																{leistung.startZeitFormatiert} -{" "}
																{leistung.endZeitFormatiert}
																<div className="text-xs text-gray-400">
																	{leistung.art}
																</div>
															</TableCell>
															<TableCell>
																{leistung.mitarbeiterName && (
																	<div>{leistung.mitarbeiterName}</div>
																)}
																<div className="text-xs text-gray-400">
																	{formatHours(leistung.stunden)}
																</div>
															</TableCell>
															<TableCell className="max-w-xs">
																<div className="truncate">
																	{leistung.beschreibung}
																</div>
															</TableCell>
															{lieferschein.status === "entwurf" && (
																<TableCell>
																	<Button
																		variant="ghost"
																		size="icon"
																		onClick={() =>
																			handleRemoveLeistung(leistung._id)
																		}
																		className="h-8 w-8 text-gray-400 hover:text-red-400"
																		title="Entfernen"
																	>
																		<Trash2 className="h-4 w-4" />
																		<span className="sr-only">Entfernen</span>
																	</Button>
																</TableCell>
															)}
														</TableRow>
													))}
													<TableRow className="bg-gray-800/30">
														<TableCell
															colSpan={
																lieferschein.status === "entwurf" ? 2 : 2
															}
															className="font-medium text-right"
														>
															Gesamtstunden:
														</TableCell>
														<TableCell className="font-medium">
															{formatHours(
																leistungen.reduce(
																	(sum, l) => sum + l.stunden,
																	0,
																),
															)}
														</TableCell>
														<TableCell
															colSpan={
																lieferschein.status === "entwurf" ? 2 : 1
															}
														></TableCell>
													</TableRow>
												</>
											)}
										</TableBody>
									</Table>
								</div>
							</CardContent>
						</Card>
					)}
				</div>
			</div>

			{/* Dialogs */}
			{isAddLeistungDialogOpen &&
				lieferscheinId &&
				lieferschein.status === "entwurf" && (
					<AddLeistungDialog
						isOpen={isAddLeistungDialogOpen}
						onClose={() => setIsAddLeistungDialogOpen(false)}
						lieferscheinId={lieferscheinId}
						kundenId={lieferschein.kundenId}
						existingLeistungIds={leistungen.map((l) => l._id)}
					/>
				)}

			{isCorrectionDialogOpen && lieferscheinId && (
				<CreateCorrectionDialog
					isOpen={isCorrectionDialogOpen}
					onClose={() => setIsCorrectionDialogOpen(false)}
					originalId={lieferscheinId}
					currentLeistungen={leistungen.map((l) => ({
						...l,
						mitarbeiterName: l.mitarbeiterName || "Unbekannt",
						kontingentName: l.kontingentName || "Unbekannt",
					}))}
				/>
			)}

			{isFinalizationDialogOpen && lieferscheinId && (
				<FinalizeLieferscheinDialog
					isOpen={isFinalizationDialogOpen}
					onClose={() => setIsFinalizationDialogOpen(false)}
					lieferscheinId={lieferscheinId}
					isCorrection={lieferschein.istKorrektur}
				/>
			)}
		</PageLayout>
	);
}

export default LieferscheinDetailPage;
