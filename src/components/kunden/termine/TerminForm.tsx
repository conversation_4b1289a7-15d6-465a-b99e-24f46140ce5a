import type { Doc } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/_shared/Dialog";
import { Input } from "@/components/_shared/Input";
import { Label } from "@/components/_shared/Label";
import { Textarea } from "@/components/_shared/Textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/_shared/select";
import { useEffect, useState } from "react";

interface TerminFormProps {
	isOpen: boolean;
	onClose: () => void;
	onSubmit: (data: any) => void;
	initialData?: Doc<"kunden_termine"> | null;
	title: string;
}

interface TerminFormData {
	titel: string;
	kategorie: string;
	datum: string;
	uhrzeit: string;
	notizen: string;
	istWiederholend: boolean;
	wiederholungsIntervall:
		| "taeglich"
		| "woechentlich"
		| "monatlich"
		| "jaehrlich"
		| "";
	wochentag:
		| "montag"
		| "dienstag"
		| "mittwoch"
		| "donnerstag"
		| "freitag"
		| "samstag"
		| "sonntag"
		| "";
	monatlicheWiederholung:
		| "erster_montag"
		| "erster_dienstag"
		| "erster_mittwoch"
		| "erster_donnerstag"
		| "erster_freitag"
		| "letzter_montag"
		| "letzter_dienstag"
		| "letzter_mittwoch"
		| "letzter_donnerstag"
		| "letzter_freitag"
		| "tag_1"
		| "tag_15"
		| "letzter_tag"
		| "";
	wiederholungsEnde: string;
}

const defaultFormData: TerminFormData = {
	titel: "",
	kategorie: "",
	datum: "",
	uhrzeit: "",
	notizen: "",
	istWiederholend: false,
	wiederholungsIntervall: "",
	wochentag: "",
	monatlicheWiederholung: "",
	wiederholungsEnde: "",
};

export function TerminForm({
	isOpen,
	onClose,
	onSubmit,
	initialData,
	title,
}: TerminFormProps) {
	const [formData, setFormData] = useState<TerminFormData>(defaultFormData);
	const [isSubmitting, setIsSubmitting] = useState(false);

	useEffect(() => {
		if (initialData) {
			setFormData({
				titel: initialData.titel,
				kategorie: initialData.kategorie,
				datum: initialData.datum || "",
				uhrzeit: initialData.uhrzeit || "",
				notizen: initialData.notizen || "",
				istWiederholend: initialData.istWiederholend,
				wiederholungsIntervall: initialData.wiederholungsIntervall || "",
				wochentag: initialData.wochentag || "",
				monatlicheWiederholung: initialData.monatlicheWiederholung || "",
				wiederholungsEnde: initialData.wiederholungsEnde || "",
			});
		} else {
			setFormData(defaultFormData);
		}
	}, [initialData, isOpen]);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setIsSubmitting(true);

		try {
			const submitData: any = {
				titel: formData.titel,
				kategorie: formData.kategorie,
				istWiederholend: formData.istWiederholend,
			};

			// Für einmalige Termine: Datum ist Pflicht
			if (!formData.istWiederholend) {
				submitData.datum = formData.datum;
			}

			// Optional fields
			if (formData.uhrzeit.trim()) {
				submitData.uhrzeit = formData.uhrzeit;
			}
			if (formData.notizen.trim()) {
				submitData.notizen = formData.notizen;
			}

			// Wiederholungsfelder
			if (formData.istWiederholend && formData.wiederholungsIntervall) {
				submitData.wiederholungsIntervall = formData.wiederholungsIntervall;

				if (
					formData.wiederholungsIntervall === "woechentlich" &&
					formData.wochentag
				) {
					submitData.wochentag = formData.wochentag;
				}

				if (
					formData.wiederholungsIntervall === "monatlich" &&
					formData.monatlicheWiederholung
				) {
					submitData.monatlicheWiederholung = formData.monatlicheWiederholung;
				}

				if (formData.wiederholungsEnde.trim()) {
					submitData.wiederholungsEnde = formData.wiederholungsEnde;
				}
			}

			await onSubmit(submitData);
		} catch (error) {
			console.error("Error submitting form:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleInputChange = (field: keyof TerminFormData, value: string) => {
		setFormData((prev) => ({
			...prev,
			[field]: field === "istWiederholend" ? value === "true" : value,
		}));
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="sm:max-w-[600px]">
				<DialogHeader>
					<DialogTitle>{title}</DialogTitle>
					<DialogDescription>
						Füllen Sie die Termindetails aus.
					</DialogDescription>
				</DialogHeader>

				<form onSubmit={handleSubmit} className="space-y-4">
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						{/* Titel */}
						<div className="md:col-span-2">
							<Label htmlFor="titel" className="text-sm font-medium">
								Titel *
							</Label>
							<Input
								id="titel"
								value={formData.titel}
								onChange={(e) => handleInputChange("titel", e.target.value)}
								placeholder="Termin-Titel eingeben"
								required
								className="mt-1"
							/>
						</div>

						{/* Kategorie */}
						<div className="md:col-span-2">
							<Label htmlFor="kategorie" className="text-sm font-medium">
								Kategorie *
							</Label>
							<Input
								id="kategorie"
								value={formData.kategorie}
								onChange={(e) => handleInputChange("kategorie", e.target.value)}
								placeholder="z.B. Hardware, Software, Serverwartung"
								required
								className="mt-1"
							/>
						</div>

						{/* Wiederholung */}
						<div className="md:col-span-2">
							<div className="flex items-center space-x-2">
								<input
									type="checkbox"
									id="istWiederholend"
									checked={formData.istWiederholend}
									onChange={(e) =>
										handleInputChange(
											"istWiederholend",
											e.target.checked.toString(),
										)
									}
									className="rounded border-gray-300"
								/>
								<Label
									htmlFor="istWiederholend"
									className="text-sm font-medium"
								>
									Wiederholender Termin
								</Label>
							</div>
						</div>

						{/* Datum (nur für einmalige Termine) */}
						{!formData.istWiederholend && (
							<div>
								<Label htmlFor="datum" className="text-sm font-medium">
									Datum *
								</Label>
								<Input
									id="datum"
									type="date"
									value={formData.datum}
									onChange={(e) => handleInputChange("datum", e.target.value)}
									required
									className="mt-1"
								/>
							</div>
						)}

						{/* Uhrzeit */}
						<div>
							<Label htmlFor="uhrzeit" className="text-sm font-medium">
								Uhrzeit
							</Label>
							<Input
								id="uhrzeit"
								type="time"
								value={formData.uhrzeit}
								onChange={(e) => handleInputChange("uhrzeit", e.target.value)}
								className="mt-1"
							/>
						</div>

						{/* Wiederholungsintervall */}
						{formData.istWiederholend && (
							<div>
								<Label
									htmlFor="wiederholungsIntervall"
									className="text-sm font-medium"
								>
									Wiederholung *
								</Label>
								<Select
									value={formData.wiederholungsIntervall}
									onValueChange={(value) =>
										handleInputChange("wiederholungsIntervall", value)
									}
								>
									<SelectTrigger className="mt-1">
										<SelectValue placeholder="Intervall wählen" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="taeglich">Täglich</SelectItem>
										<SelectItem value="woechentlich">Wöchentlich</SelectItem>
										<SelectItem value="monatlich">Monatlich</SelectItem>
										<SelectItem value="jaehrlich">Jährlich</SelectItem>
									</SelectContent>
								</Select>
							</div>
						)}

						{/* Wochentag (für wöchentliche Wiederholung) */}
						{formData.istWiederholend &&
							formData.wiederholungsIntervall === "woechentlich" && (
								<div>
									<Label htmlFor="wochentag" className="text-sm font-medium">
										Wochentag *
									</Label>
									<Select
										value={formData.wochentag}
										onValueChange={(value) =>
											handleInputChange("wochentag", value)
										}
									>
										<SelectTrigger className="mt-1">
											<SelectValue placeholder="Wochentag wählen" />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="montag">Montag</SelectItem>
											<SelectItem value="dienstag">Dienstag</SelectItem>
											<SelectItem value="mittwoch">Mittwoch</SelectItem>
											<SelectItem value="donnerstag">Donnerstag</SelectItem>
											<SelectItem value="freitag">Freitag</SelectItem>
											<SelectItem value="samstag">Samstag</SelectItem>
											<SelectItem value="sonntag">Sonntag</SelectItem>
										</SelectContent>
									</Select>
								</div>
							)}

						{/* Monatliche Wiederholung */}
						{formData.istWiederholend &&
							formData.wiederholungsIntervall === "monatlich" && (
								<div>
									<Label
										htmlFor="monatlicheWiederholung"
										className="text-sm font-medium"
									>
										Monatliche Wiederholung *
									</Label>
									<Select
										value={formData.monatlicheWiederholung}
										onValueChange={(value) =>
											handleInputChange("monatlicheWiederholung", value)
										}
									>
										<SelectTrigger className="mt-1">
											<SelectValue placeholder="Wiederholung wählen" />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="tag_1">1. des Monats</SelectItem>
											<SelectItem value="tag_15">15. des Monats</SelectItem>
											<SelectItem value="letzter_tag">
												Letzter Tag des Monats
											</SelectItem>
											<SelectItem value="erster_montag">
												Erster Montag
											</SelectItem>
											<SelectItem value="erster_dienstag">
												Erster Dienstag
											</SelectItem>
											<SelectItem value="erster_mittwoch">
												Erster Mittwoch
											</SelectItem>
											<SelectItem value="erster_donnerstag">
												Erster Donnerstag
											</SelectItem>
											<SelectItem value="erster_freitag">
												Erster Freitag
											</SelectItem>
											<SelectItem value="letzter_montag">
												Letzter Montag
											</SelectItem>
											<SelectItem value="letzter_dienstag">
												Letzter Dienstag
											</SelectItem>
											<SelectItem value="letzter_mittwoch">
												Letzter Mittwoch
											</SelectItem>
											<SelectItem value="letzter_donnerstag">
												Letzter Donnerstag
											</SelectItem>
											<SelectItem value="letzter_freitag">
												Letzter Freitag
											</SelectItem>
										</SelectContent>
									</Select>
								</div>
							)}

						{/* Wiederholungsende */}
						{formData.istWiederholend && (
							<div>
								<Label
									htmlFor="wiederholungsEnde"
									className="text-sm font-medium"
								>
									Wiederholung bis
								</Label>
								<Input
									id="wiederholungsEnde"
									type="date"
									value={formData.wiederholungsEnde}
									onChange={(e) =>
										handleInputChange("wiederholungsEnde", e.target.value)
									}
									className="mt-1"
								/>
							</div>
						)}

						{/* Notizen */}
						<div className="md:col-span-2">
							<Label htmlFor="notizen" className="text-sm font-medium">
								Notizen
							</Label>
							<Textarea
								id="notizen"
								value={formData.notizen}
								onChange={(e) => handleInputChange("notizen", e.target.value)}
								placeholder="Zusätzliche Notizen"
								rows={3}
								className="mt-1"
							/>
						</div>
					</div>

					<DialogFooter>
						<Button
							type="button"
							variant="outline"
							onClick={onClose}
							disabled={isSubmitting}
						>
							Abbrechen
						</Button>
						<Button type="submit" disabled={isSubmitting}>
							{isSubmitting ? "Speichern..." : "Speichern"}
						</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
