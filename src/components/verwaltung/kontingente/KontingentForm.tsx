import { api } from "@/../convex/_generated/api";
import { Doc, Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import { Input } from "@/components/_shared/Input";
import { Label } from "@/components/_shared/Label";
import { ModalFormDialog } from "@/components/_shared/ModalFormDialog";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/_shared/select";
import { toInputDate } from "@/lib/utils/formatUtils";
import { useMutation, useQuery } from "convex/react";
import { CalendarCheck, Check, Pencil, PlusCircle } from "lucide-react";
import React, { useState, useEffect, useMemo } from "react";
import { toast } from "sonner";

// Interface (kann importiert oder hier definiert werden)
interface KontingentMitKunde {
	_id: Id<"kunden_kontingente">;
	name: string;
	kundenId: Id<"kunden">;
	stunden: number;
	verbrauchteStunden: number;
	startDatum: number;
	endDatum: number;
	istAktiv: boolean;
	// Weitere Felder ggf. hinzufügen, wenn im Formular direkt benötigt
}
// KundeDoc type definition might need adjustment if the structure from kunden/stammdaten changes significantly.
// For now, assuming Doc<"kunden"> is still a valid representation for the list of customers.
type KundeDoc = Doc<"kunden">;

interface KontingentFormProps {
	editingKontingent: KontingentMitKunde | null;
	kunden: KundeDoc[]; // Liste der Kunden für das Dropdown
	initialData: KontingentFormData; // Added for default end date
	isEditing: boolean; // Added to distinguish logic
	onSubmitSuccess: () => void;
	onCancel: () => void;
}

// Added for form state typing
interface KontingentFormData {
	kundeId: string;
	kontingentName: string;
	stunden: string;
	verbrauchteStunden: string;
	startDatumInput: string;
	endDatumInput: string; // New field
	istAktiv: boolean;
}

// Helper (könnten auch in eine utils-Datei)
const toTimestamp = (dateString: string): number | undefined => {
	if (!dateString) return undefined;
	try {
		const date = new Date(dateString);
		// Ensure the time is set to the beginning of the day for consistency if needed,
		// or handle timezone explicitly if it's an issue.
		// For now, direct conversion is used.
		return date.getTime();
	} catch (e) {
		console.error("Error parsing date string to timestamp:", dateString, e);
		return undefined;
	}
};
// Hilfsfunktion zur Berechnung der Tage zwischen zwei Daten
const calculateDaysBetween = (
	startDateStr: string,
	endDateStr: string,
): number => {
	if (!startDateStr || !endDateStr) return 0;
	try {
		const startDate = new Date(startDateStr);
		const endDate = new Date(endDateStr);
		// Zeiten auf Mitternacht setzen für genauen Tagesvergleich
		startDate.setHours(0, 0, 0, 0);
		endDate.setHours(0, 0, 0, 0);

		// Differenz in Millisekunden berechnen und in Tage umwandeln
		const diffTime = endDate.getTime() - startDate.getTime();
		// +1 weil wir den Start- und Endtag mitzählen wollen
		const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
		return diffDays > 0 ? diffDays : 0;
	} catch (e) {
		return 0;
	}
};

export function KontingentForm({
	editingKontingent,
	kunden,
	initialData,
	isEditing,
	onSubmitSuccess,
	onCancel,
}: KontingentFormProps) {
	const createKontingent = useMutation(api.verwaltung.kontingente.create);
	const updateKontingent = useMutation(api.verwaltung.kontingente.update);
	const kundenQuery = useQuery(api.verwaltung.kunden.list) || [];

	const [formState, setFormState] = useState<KontingentFormData>(initialData);

	// Berechne die Anzahl der Tage zwischen Start- und Enddatum
	const daysBetween = useMemo(() => {
		return calculateDaysBetween(
			formState.startDatumInput,
			formState.endDatumInput,
		);
	}, [formState.startDatumInput, formState.endDatumInput]);

	useEffect(() => {
		if (editingKontingent) {
			setFormState({
				kundeId: editingKontingent.kundenId,
				kontingentName: editingKontingent.name,
				stunden: editingKontingent.stunden.toString(),
				verbrauchteStunden: editingKontingent.verbrauchteStunden.toString(),
				startDatumInput: toInputDate(editingKontingent.startDatum),
				endDatumInput: toInputDate(editingKontingent.endDatum),
				istAktiv: editingKontingent.istAktiv,
			});
		} else {
			setFormState(initialData);
		}
	}, [editingKontingent, initialData]);

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { id, value } = e.target;
		setFormState((prev) => ({ ...prev, [id]: value }));
	};

	const handleSelectChange = (
		id: keyof KontingentFormData,
		value: string | boolean,
	) => {
		setFormState((prev) => ({ ...prev, [id]: value }));
	};

	const handleSubmit = () => {
		const {
			kundeId,
			kontingentName,
			stunden,
			verbrauchteStunden,
			startDatumInput,
			endDatumInput,
			istAktiv,
		} = formState;

		const startDatumTs = toTimestamp(startDatumInput);
		const endDatumTs = toTimestamp(endDatumInput);

		if (!endDatumTs) {
			toast.error("Bitte ein gültiges Enddatum angeben.");
			return;
		}

		if (startDatumTs && endDatumTs && endDatumTs <= startDatumTs) {
			toast.error("Das Enddatum muss nach dem Startdatum liegen.");
			return;
		}

		if (editingKontingent) {
			if (!kontingentName) {
				toast.error("Bitte einen Namen für das Kontingent angeben.");
				return;
			}
			const updateArgs = {
				id: editingKontingent._id,
				name: kontingentName,
				stunden: stunden ? Number.parseFloat(stunden) : undefined,
				verbrauchteStunden: verbrauchteStunden
					? Number.parseFloat(verbrauchteStunden)
					: undefined,
				istAktiv: istAktiv,
				startDatum: startDatumTs,
				endDatum: endDatumTs,
			};

			updateKontingent(updateArgs)
				.then(() => {
					toast.success("Kontingent erfolgreich aktualisiert");
					onSubmitSuccess();
				})
				.catch((error: Error) => {
					toast.error(
						`Fehler beim Speichern: ${error.message || "Unbekannter Fehler"}`,
					);
				});
		} else {
			if (!kundeId || !stunden || !kontingentName) {
				toast.error("Bitte Kunde, Stunden und Namen angeben.");
				return;
			}
			const createArgs = {
				kundenId: kundeId as Id<"kunden">,
				name: kontingentName,
				stunden: Number.parseFloat(stunden),
				startDatum: startDatumTs,
				endDatum: endDatumTs,
			};
			createKontingent(createArgs)
				.then(() => {
					toast.success("Kontingent erfolgreich angelegt");
					onSubmitSuccess();
				})
				.catch((error: Error) => {
					toast.error(
						`Fehler beim Speichern: ${error.message || "Unbekannter Fehler"}`,
					);
				});
		}
	};

	return (
		<ModalFormDialog
			isOpen={true}
			onClose={onCancel}
			title={
				editingKontingent ? "Kontingent bearbeiten" : "Neues Kontingent anlegen"
			}
			icon={
				editingKontingent ? (
					<Pencil className="h-3.5 w-3.5" />
				) : (
					<PlusCircle className="h-3.5 w-3.5" />
				)
			}
			footerAction={{
				label: editingKontingent ? "Aktualisieren" : "Kontingent anlegen",
				onClick: handleSubmit,
				icon: <Check className="h-4 w-4" />,
			}}
			maxWidth="xl"
		>
			<div className="space-y-5">
				<div
					className={`grid grid-cols-1 ${editingKontingent ? "md:grid-cols-4 lg:grid-cols-4" : "md:grid-cols-3"} gap-4`}
				>
					{/* Kunde nur beim Anlegen auswählen */}
					{!editingKontingent && (
						<div>
							<Label
								htmlFor="kundeId"
								className="text-xs text-gray-400 mb-1 block"
							>
								Kunde *
							</Label>
							<Select
								onValueChange={(value) => handleSelectChange("kundeId", value)}
								value={formState.kundeId}
								required
							>
								<SelectTrigger
									id="kundeId"
									className="h-9 bg-gray-800/60 border-gray-700"
								>
									<SelectValue placeholder="Kunde auswählen..." />
								</SelectTrigger>
								<SelectContent>
									{kundenQuery.map((kunde) => (
										<SelectItem key={kunde._id} value={kunde._id}>
											{kunde.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
					)}

					{/* Name (immer sichtbar/änderbar) */}
					<div
						className={editingKontingent ? "md:col-span-2 lg:col-span-1" : ""}
					>
						<Label
							htmlFor="kontingentName"
							className="text-xs text-gray-400 mb-1 block"
						>
							Kontingent Name *
						</Label>
						<Input
							id="kontingentName"
							type="text"
							value={formState.kontingentName}
							onChange={handleInputChange}
							required
							placeholder="z.B. Support Q3/24"
							className="h-9 bg-gray-800/60 border-gray-700"
						/>
					</div>

					{/* Startdatum (immer sichtbar/änderbar) */}
					<div>
						<Label
							htmlFor="startDatumInput"
							className="text-xs text-gray-400 mb-1 block"
						>
							Startdatum
						</Label>
						<div className="relative">
							<Input
								id="startDatumInput"
								type="date"
								value={formState.startDatumInput}
								onChange={handleInputChange}
								className="h-9 bg-gray-800/60 border-gray-700 pl-9"
							/>
							<CalendarCheck className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						</div>
					</div>

					{/* Enddatum (NEU) */}
					<div>
						<Label
							htmlFor="endDatumInput"
							className="text-xs text-gray-400 mb-1 block"
						>
							Enddatum *
						</Label>
						<div className="relative">
							<Input
								id="endDatumInput"
								type="date"
								value={formState.endDatumInput}
								onChange={handleInputChange}
								required
								className="h-9 bg-gray-800/60 border-gray-700 pl-9"
							/>
							<CalendarCheck className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						</div>
					</div>

					{/* Stunden */}
					<div>
						<Label
							htmlFor="stunden"
							className="text-xs text-gray-400 mb-1 block"
						>
							Stunden gesamt *
						</Label>
						<Input
							id="stunden"
							type="number"
							value={formState.stunden}
							onChange={handleInputChange}
							required
							min="0.25"
							step="0.25"
							placeholder="z.B. 40"
							className="h-9 bg-gray-800/60 border-gray-700"
						/>
					</div>

					{/* Verbrauchte Stunden & Aktiv-Status nur beim Bearbeiten */}
					{editingKontingent && (
						<>
							<div>
								<Label
									htmlFor="verbrauchteStunden"
									className="text-xs text-gray-400 mb-1 block"
								>
									Verbrauchte Stunden
								</Label>
								<Input
									id="verbrauchteStunden"
									type="number"
									value={formState.verbrauchteStunden}
									onChange={handleInputChange}
									min="0"
									step="0.25"
									placeholder="0.00"
									className="h-9 bg-gray-800/60 border-gray-700"
								/>
							</div>

							<div>
								<Label
									htmlFor="istAktiv"
									className="text-xs text-gray-400 mb-1 block"
								>
									Status
								</Label>
								<Select
									onValueChange={(val: string) =>
										handleSelectChange("istAktiv", val === "true")
									}
									value={String(formState.istAktiv)}
								>
									<SelectTrigger
										id="istAktiv"
										className="h-9 bg-gray-800/60 border-gray-700"
									>
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="true">Aktiv</SelectItem>
										<SelectItem value="false">Inaktiv</SelectItem>
									</SelectContent>
								</Select>
							</div>
						</>
					)}
				</div>
				{/* Zusammenfassung */}
				<div className="flex flex-wrap items-center gap-x-4 gap-y-2 text-sm pt-4 border-t border-gray-700/30">
					<div className="flex items-center gap-1.5">
						<CalendarCheck className="h-4 w-4 text-gray-400" />
						<span>Laufzeit: {daysBetween} Tage</span>
					</div>
				</div>
			</div>
		</ModalFormDialog>
	);
}
