import { api } from "@/../convex/_generated/api";
import type { Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/components/_shared/Card";
import { Skeleton } from "@/components/_shared/Skeleton";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import { DokuEntryForm } from "@/components/kunden/doku/DokuEntryForm";
import { PageLayout } from "@/components/layout/PageLayout";
import {
	calculateTimeRemaining,
	formatDate,
	formatTime,
} from "@/lib/utils/dateUtils";
import { useMutation, useQuery } from "convex/react";
import {
	ArrowLeft,
	Calendar,
	Check,
	Copy,
	Download,
	Eye,
	EyeOff,
	Mail,
	MapPin,
	NotebookText,
	Pencil,
	PlusCircle,
	Repeat,
	Trash2,
	X,
} from "lucide-react";
import { useEffect, useMemo, useRef, useState } from "react";
import { Link, useParams } from "react-router-dom";
import { toast } from "sonner";

import type { Doc } from "@/../convex/_generated/dataModel";
import { FIELD_TYPES } from "@/../convex/schema";
import {
	Dialog,
	DialogClose,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@/components/_shared/Dialog";
import VCard from "vcf";
import type { Kunde } from "../../verwaltung/kunden/index";
import type { AnsprechpartnerData } from "@/components/verwaltung/kunden/KundenForm";

// Define DokuEintragMitInfo to match the output of getByKunde which includes categoryName and kundeName
interface DokuEintragMitInfo {
	_id: Id<"kunden_dokumentation">;
	_creationTime: number;
	kundenId: Id<"kunden">;
	kategorieID: number; // Numeric ID of the category
	feldwerte: { feldId: number; feldWert: string; feldName?: string }[];
	kategorieName: string; // Added from getByKunde
	// kundeName: string; // kundeName is also available from getByKunde but might not be needed for filtering here
}

export function KundenDokuDetailPage() {
	const { id } = useParams<{ id: string }>();
	const kundenId = id as Id<"kunden">;
	const pageTopRef = useRef<HTMLDivElement>(null); // Ref for scrolling to top of form

	const kunde = useQuery(api.verwaltung.kunden.get, { id: kundenId }) as
		| Doc<"kunden">
		| null
		| undefined;
	const alleKategorien = useQuery(api.system.dokuKategorien.list) || [];
	// Fetch all documentation entries for THIS customer
	const alleEintraegeFuerKunde: DokuEintragMitInfo[] =
		useQuery(api.kunden.dokumentation.getByKunde, { kundenId }) || [];
	// Fetch appointments for THIS customer
	const termine = useQuery(api.kunden.termine.getByKunde, { kundenId }) || [];

	const initializeDefaults = useMutation(
		api.system.dokuKategorien.initializeDefaults,
	);
	const removeEintrag = useMutation(api.kunden.dokumentation.remove);

	const [editingState, setEditingState] = useState<{
		kategorieId: Id<"system_doku_kategorien"> | null;
		eintragId: Id<"kunden_dokumentation"> | null;
	}>({ kategorieId: null, eintragId: null });

	const [isInitializingDefaults, setIsInitializingDefaults] = useState(false);
	const [hiddenFieldsVisibility, setHiddenFieldsVisibility] = useState<
		Record<string, boolean>
	>({}); // For visibility of fields with istVersteckt=true per category ID
	const [passwordCopiedStates, setPasswordCopiedStates] = useState<
		Record<string, boolean>
	>({});
	const [copiedStates, setCopiedStates] = useState<Record<string, boolean>>({}); // State for copy feedback

	useEffect(() => {
		if (alleKategorien.length === 0 && !isInitializingDefaults) {
			setIsInitializingDefaults(true);
			initializeDefaults({})
				.then((result) => {
					toast.info(result + " (Automatische Erstinitialisierung)");
				})
				.catch((err) => {
					toast.error("Fehler bei Initialisierung der Standardkategorien.");
					console.error(err);
				})
				.finally(() => {
					setIsInitializingDefaults(false);
				});
		}
	}, [alleKategorien, initializeDefaults, isInitializingDefaults]);

	const showFormForKategorie = editingState.kategorieId;
	const editingEintragId = editingState.eintragId;

	const sortedKategorien = useMemo(() => {
		return alleKategorien.sort((a, b) => a.reihenfolge - b.reihenfolge);
	}, [alleKategorien]);

	const handleFormSubmitSuccess = () => {
		setEditingState({ kategorieId: null, eintragId: null });
		toast.success("Dokumentationseintrag gespeichert.");
	};

	const handleFormCancel = () => {
		setEditingState({ kategorieId: null, eintragId: null });
	};

	const handleAddEntry = (kategorieId: Id<"system_doku_kategorien">) => {
		setEditingState({ kategorieId: kategorieId, eintragId: null });
	};

	const handleEditEntry = (
		kategorieId: Id<"system_doku_kategorien">,
		eintragId: Id<"kunden_dokumentation">,
	) => {
		setEditingState({ kategorieId: kategorieId, eintragId: eintragId });
	};

	// Modified copy helper slightly for password feedback state
	const copyToClipboard = (text: string, id: string, isPassword = false) => {
		navigator.clipboard.writeText(text).then(
			() => {
				if (isPassword) {
					setPasswordCopiedStates((prev) => ({ ...prev, [id]: true }));
					toast.success("Passwort kopiert!");
					setTimeout(
						() => setPasswordCopiedStates((prev) => ({ ...prev, [id]: false })),
						1500,
					);
				} else {
					setCopiedStates((prev) => ({ ...prev, [id]: true }));
					toast.success("In Zwischenablage kopiert!");
					setTimeout(
						() => setCopiedStates((prev) => ({ ...prev, [id]: false })),
						1500,
					);
				}
			},
			(err) => {
				toast.error("Fehler beim Kopieren.");
			},
		);
	};

	const toggleHiddenFieldsVisibility = (kategorieId: string) => {
		setHiddenFieldsVisibility((prev) => ({
			...prev,
			[kategorieId]: !prev[kategorieId],
		}));
	};

	const handleDeleteEntry = async (id: Id<"kunden_dokumentation">) => {
		if (
			window.confirm(
				"Möchten Sie diesen Dokumentationseintrag wirklich löschen?",
			)
		) {
			try {
				await removeEintrag({ id });
				toast.success("Eintrag erfolgreich gelöscht.");
				// Optionally refetch data or update local state if needed,
				// but Convex usually handles reactivity automatically.
			} catch (error) {
				console.error("Fehler beim Löschen des Eintrags:", error);
				toast.error("Fehler beim Löschen: " + (error as Error).message);
			}
		}
	};

	const handleDownloadVCard = (
		partner: AnsprechpartnerData,
		kundeFull: Doc<"kunden">,
	) => {
		const card = new VCard();
		card.set("version", "3.0");

		const nameParts = partner.name.split(" ");
		const lastName = nameParts.pop() || "";
		const firstName = nameParts.join(" ") || "";

		card.set("fn", partner.name);
		card.set("n", `${lastName};${firstName}`);

		if (partner.email) {
			card.add("email", partner.email, { type: "work" });
		}

		// Use card.add for multiple phone numbers
		if (partner.telefon) {
			card.add("tel", partner.telefon, { type: "work,voice" });
		}
		if (partner.mobil) {
			card.add("tel", partner.mobil, { type: "cell,voice" });
		}

		if (partner.position) card.set("title", partner.position);
		card.set("org", kundeFull.name);

		const hauptstandort = kundeFull.standorte?.find(
			(s: Kunde["standorte"][number]) => s.istHauptstandort,
		);
		if (hauptstandort) {
			const adrFields = [
				"", // PO Box
				"", // Extended Address
				hauptstandort.strasse || "",
				hauptstandort.ort || "",
				"", // Region
				hauptstandort.plz || "",
				hauptstandort.land || "",
			];
			// For ADR, typically one entry per type (e.g., work, home). Using set is fine.
			card.set("adr", adrFields.join(";"), { type: "work" });
		}

		// The vcf library should handle UTF-8 encoding for values internally for vCard 3.0.
		// The Blob is already correctly set to use charset=utf-8.
		const vCardString = card.toString();
		const blob = new Blob([vCardString], { type: "text/vcard;charset=utf-8;" });
		const link = document.createElement("a");
		link.href = URL.createObjectURL(blob);
		link.setAttribute(
			"download",
			`${partner.name.replace(/[^a-z0-9]/gi, "_")}_${kundeFull.name.replace(/[^a-z0-9]/gi, "_")}.vcf`,
		);
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
		toast.success("vCard wird heruntergeladen...");
	};

	const pageActionButtons = (
		<Link to="/kunden/doku">
			<Button variant="outline" size="sm" className="gap-1">
				<ArrowLeft className="h-4 w-4" /> Zurück zur Übersicht
			</Button>
		</Link>
	);

	if (
		kunde === undefined ||
		alleKategorien === undefined ||
		alleEintraegeFuerKunde === undefined
	) {
		return (
			<PageLayout
				ref={pageTopRef}
				title="Kundendokumentation"
				subtitle="Dokumentation wird geladen..."
				action={pageActionButtons}
			>
				<Card className="shadow-lg border-0">
					<CardHeader className="pb-3 px-5 border-b border-gray-700/50">
						<Skeleton className="h-6 w-48" />
					</CardHeader>
					<CardContent className="p-6 space-y-4">
						<Skeleton className="h-4 w-full" />
						<Skeleton className="h-4 w-3/4" />
						<Skeleton className="h-4 w-5/6" />
					</CardContent>
				</Card>
			</PageLayout>
		);
	}

	if (kunde === null) {
		return (
			<PageLayout
				ref={pageTopRef}
				title="Fehler"
				subtitle="Kunde nicht gefunden."
			>
				<p>Der angeforderte Kunde konnte nicht gefunden werden.</p>
				<Link to="/kunden/doku">
					<Button variant="outline" className="mt-4">
						Zurück zur Übersicht
					</Button>
				</Link>
			</PageLayout>
		);
	}

	return (
		<PageLayout
			ref={pageTopRef}
			title={`Dokumentation: ${kunde.name}`}
			subtitle="Verwalten Sie hier die Dokumentation für diesen Kunden"
			action={pageActionButtons}
		>
			<div className="space-y-8">
				{/* Display Standorte as Table */}
				<Card className="shadow-lg border-0 overflow-hidden">
					<CardHeader className="pb-3 px-5 border-b border-gray-700/50">
						<CardTitle className="text-lg font-medium">
							Standorte (aus Stammdaten)
						</CardTitle>
					</CardHeader>
					<CardContent className="p-0">
						{kunde.standorte && kunde.standorte.length > 0 ? (
							<Table>
								<TableHeader>
									<TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
										<TableHead className="w-12 text-center py-2 px-3 text-xs">
											Haupt
										</TableHead>
										<TableHead className="py-2 px-3 text-xs">Straße</TableHead>
										<TableHead className="py-2 px-3 text-xs">PLZ</TableHead>
										<TableHead className="py-2 px-3 text-xs">Ort</TableHead>
										<TableHead className="py-2 px-3 text-xs">Land</TableHead>
										<TableHead className="w-24 text-center py-2 px-3 text-xs">
											Aktionen
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{kunde.standorte.map((standort, index) => {
										const addressString = `${standort.strasse}, ${standort.plz} ${standort.ort}${standort.land ? `, ${standort.land}` : ""}`;
										const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(addressString)}`;
										const copyId = `standort-${index}`;
										return (
											<TableRow key={index}>
												<TableCell className="text-center py-1.5 px-3 text-xs">
													{standort.istHauptstandort && (
														<Check className="h-5 w-5 text-green-400 mx-auto" />
													)}
												</TableCell>
												<TableCell className="py-1.5 px-3 text-xs">
													{standort.strasse}
												</TableCell>
												<TableCell className="py-1.5 px-3 text-xs">
													{standort.plz}
												</TableCell>
												<TableCell className="py-1.5 px-3 text-xs">
													{standort.ort}
												</TableCell>
												<TableCell className="py-1.5 px-3 text-xs">
													{standort.land || "-"}
												</TableCell>
												<TableCell className="text-center py-1.5 px-3 text-xs">
													<div className="flex justify-center gap-1">
														<Button
															variant="ghost"
															size="icon"
															onClick={() =>
																copyToClipboard(addressString, copyId)
															}
															className={`h-6 w-6 ${copiedStates[copyId] ? "text-green-400" : "text-blue-400 hover:text-blue-500"} hover:bg-blue-500/10`}
															title="Adresse kopieren"
														>
															<Copy className="h-3 w-3" />
														</Button>
														<Button
															variant="ghost"
															size="icon"
															onClick={() => window.open(mapsUrl, "_blank")}
															className="h-6 w-6 text-purple-400 hover:text-purple-500 hover:bg-purple-500/10"
															title="In Karte öffnen"
														>
															<MapPin className="h-3 w-3" />
														</Button>
													</div>
												</TableCell>
											</TableRow>
										);
									})}
								</TableBody>
							</Table>
						) : (
							<p className="text-gray-400 p-5">
								Keine Standorte in den Stammdaten hinterlegt.
							</p>
						)}
					</CardContent>
				</Card>

				{/* Display Ansprechpartner as Table */}
				<Card className="shadow-lg border-0 overflow-hidden">
					<CardHeader className="pb-3 px-5 border-b border-gray-700/50">
						<CardTitle className="text-lg font-medium">
							Ansprechpartner (aus Stammdaten)
						</CardTitle>
					</CardHeader>
					<CardContent className="p-0">
						{kunde.ansprechpartner && kunde.ansprechpartner.length > 0 ? (
							<Table>
								<TableHeader>
									<TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
										<TableHead className="w-12 text-center py-2 px-3 text-xs">
											E-Mail
										</TableHead>
										<TableHead className="w-12 text-center py-2 px-3 text-xs">
											Haupt
										</TableHead>
										<TableHead className="py-2 px-3 text-xs">Name</TableHead>
										<TableHead className="py-2 px-3 text-xs">
											Position
										</TableHead>
										<TableHead className="py-2 px-3 text-xs">E-Mail</TableHead>
										<TableHead className="py-2 px-3 text-xs">Telefon</TableHead>
										<TableHead className="py-2 px-3 text-xs">Mobil</TableHead>
										<TableHead className="w-24 text-center py-2 px-3 text-xs">
											Aktionen
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{kunde.ansprechpartner.map((partner, index) => {
										return (
											<TableRow key={index}>
												<TableCell className="text-center py-1.5 px-3 text-xs">
													<div className="flex gap-1 justify-center">
														{partner.istEmailLieferscheinEmpfaenger && (
															<Mail
																className="h-3 w-3 text-blue-400"
																title="Lieferschein-Empfänger"
															/>
														)}
														{partner.istEmailUebersichtEmpfaenger && (
															<Mail
																className="h-3 w-3 text-green-400"
																title="Übersicht-Empfänger"
															/>
														)}
														{partner.istEmailAnrede && (
															<Mail
																className="h-3 w-3 text-purple-400"
																title="E-Mail-Anrede"
															/>
														)}
													</div>
												</TableCell>
												<TableCell className="text-center py-1.5 px-3 text-xs">
													{partner.istHauptansprechpartner && (
														<Check className="h-5 w-5 text-green-400 mx-auto" />
													)}
												</TableCell>
												<TableCell className="py-1.5 px-3 text-xs">
													{partner.name}
												</TableCell>
												<TableCell className="py-1.5 px-3 text-xs">
													{partner.position || "-"}
												</TableCell>
												<TableCell className="py-1.5 px-3 text-xs">
													{partner.email ? (
														<a
															href={`mailto:${partner.email}`}
															className="text-cyan-400 hover:text-cyan-300"
														>
															{partner.email}
														</a>
													) : (
														"-"
													)}
												</TableCell>
												<TableCell className="py-1.5 px-3 text-xs">
													{partner.telefon || "-"}
												</TableCell>
												<TableCell className="py-1.5 px-3 text-xs">
													{partner.mobil || "-"}
												</TableCell>
												<TableCell className="text-center py-1.5 px-3 text-xs">
													<div className="flex justify-center gap-1">
														<Button
															variant="ghost"
															size="icon"
															onClick={() =>
																kunde && handleDownloadVCard(partner, kunde)
															}
															className={`h-6 w-6 text-blue-400 hover:text-blue-500 hover:bg-blue-500/10`}
															title="Kontakt herunterladen (vCard)"
															disabled={!kunde}
														>
															<Download className="h-3 w-3" />
														</Button>
													</div>
												</TableCell>
											</TableRow>
										);
									})}
								</TableBody>
							</Table>
						) : (
							<p className="text-gray-400 p-5">
								Keine Ansprechpartner in den Stammdaten hinterlegt.
							</p>
						)}
					</CardContent>
				</Card>

				{/* Display Termine as Table */}
				<Card className="shadow-lg border-0 overflow-hidden">
					<CardHeader className="pb-3 px-5 border-b border-gray-700/50 flex flex-row justify-between items-center">
						<CardTitle className="text-lg font-medium">Termine</CardTitle>
						<Link to={`/kunden/termine/${kundenId}`}>
							<Button
								variant="ghost"
								size="icon"
								className="h-6 w-6 text-gray-400 hover:text-white hover:bg-gray-700/70"
								title="Alle Termine verwalten"
							>
								<Calendar className="h-3 w-3" />
							</Button>
						</Link>
					</CardHeader>
					<CardContent className="p-0">
						{termine && termine.length > 0 ? (
							<Table>
								<TableHeader>
									<TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
										<TableHead className="py-2 px-3 text-xs">
											Kategorie
										</TableHead>
										<TableHead className="py-2 px-3 text-xs">Titel</TableHead>
										<TableHead className="py-2 px-3 text-xs">Datum</TableHead>
										<TableHead className="py-2 px-3 text-xs">Uhrzeit</TableHead>
										<TableHead className="py-2 px-3 text-xs">
											Restlaufzeit
										</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{termine.slice(0, 5).map((termin) => {
										const timeRemaining = calculateTimeRemaining(
											termin.istWiederholend
												? termin.naechsteWiederholung
												: termin.datum,
											termin.uhrzeit,
										);

										return (
											<TableRow key={termin._id}>
												<TableCell className="py-1.5 px-3 text-xs">
													<span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-500/10 text-blue-400">
														{termin.kategorie}
													</span>
												</TableCell>
												<TableCell className="py-1.5 px-3 text-xs font-medium">
													<div className="flex items-center gap-1">
														{termin.titel}
														{termin.istWiederholend && (
															<Repeat
																className="h-3 w-3 text-blue-400"
																title="Wiederholender Termin"
															/>
														)}
													</div>
												</TableCell>
												<TableCell className="py-1.5 px-3 text-xs">
													{termin.istWiederholend
														? formatDate(termin.naechsteWiederholung || "")
														: formatDate(termin.datum || "")}
												</TableCell>
												<TableCell className="py-1.5 px-3 text-xs">
													{formatTime(termin.uhrzeit)}
												</TableCell>
												<TableCell className="py-1.5 px-3 text-xs">
													<span
														className={
															timeRemaining.startsWith("vor")
																? "text-red-400"
																: "text-green-400"
														}
													>
														{timeRemaining}
													</span>
												</TableCell>
											</TableRow>
										);
									})}
								</TableBody>
							</Table>
						) : (
							<p className="text-gray-400 p-5">Keine Termine vorhanden.</p>
						)}
					</CardContent>
				</Card>

				{sortedKategorien.map((kategorie) => {
					// Filter entries for the current category from all entries fetched for the customer
					const entriesForCurrentCategory = alleEintraegeFuerKunde.filter(
						(e: DokuEintragMitInfo) => e.kategorieID === kategorie.kategorieID,
					);

					return (
						<Card
							key={kategorie._id}
							className="shadow-lg border-0 overflow-hidden"
						>
							<CardHeader className="pb-3 px-5 border-b border-gray-700/50 flex flex-row justify-between items-center">
								<CardTitle className="text-lg font-medium">
									{kategorie.name}
								</CardTitle>
								<div className="flex items-center gap-1">
									{" "}
									{/* Group for action buttons */}
									{/* Eye button only if category has fields with istVersteckt=true AND form is NOT shown */}
									{!showFormForKategorie &&
										kategorie.felder.some((f) => f.istVersteckt) && (
											<Button
												variant="ghost"
												size="icon"
												onClick={() =>
													toggleHiddenFieldsVisibility(kategorie._id)
												}
												className="h-6 w-6 text-gray-400 hover:text-white hover:bg-gray-700/70"
												title={
													hiddenFieldsVisibility[kategorie._id]
														? "Versteckte Felder ausblenden"
														: "Versteckte Felder anzeigen"
												}
											>
												{hiddenFieldsVisibility[kategorie._id] ? (
													<EyeOff className="h-3 w-3" />
												) : (
													<Eye className="h-3 w-3" />
												)}
											</Button>
										)}
									{/* Action Buttons (Add/Close) */}
									{showFormForKategorie === kategorie._id ? (
										// Close button
										<Button
											onClick={handleFormCancel}
											size="icon"
											variant="ghost"
											className="h-6 w-6 text-gray-400 hover:text-white hover:bg-gray-700/70"
											title="Formular schließen"
										>
											<X className="h-3 w-3" />
										</Button>
									) : (
										// Add button
										<Button
											onClick={() => handleAddEntry(kategorie._id)}
											size="icon"
											variant="ghost"
											className="h-6 w-6 text-gray-400 hover:text-white hover:bg-gray-700/70"
											title={`Eintrag zu \"${kategorie.name}\" hinzufügen`}
										>
											<PlusCircle className="h-3 w-3" />
										</Button>
									)}
								</div>
							</CardHeader>
							{/* Only render CardContent if there are entries OR if the form is shown for this category */}
							{(entriesForCurrentCategory.length > 0 ||
								showFormForKategorie === kategorie._id) && (
								<CardContent className="p-0">
									{showFormForKategorie === kategorie._id ? (
										<div className="p-5">
											<DokuEntryForm
												kundenId={kundenId}
												kategorieId={kategorie._id}
												editingId={editingEintragId}
												onSubmitSuccess={handleFormSubmitSuccess}
												onCancel={handleFormCancel}
											/>
										</div>
									) : (
										<Table>
											<TableHeader>
												<TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
													{kategorie.felder
														.filter((feld) => !feld.istExtrafeld)
														.map((feld) => (
															<TableHead
																key={feld.feldId}
																className="py-2 px-3 text-xs"
															>
																{feld.name}
																{feld.istErforderlich && (
																	<span className="text-red-400 ml-0.5">*</span>
																)}
															</TableHead>
														))}
													<TableHead className="w-24 text-center py-2 px-3 text-xs">
														Aktionen
													</TableHead>
												</TableRow>
											</TableHeader>
											<TableBody>
												{entriesForCurrentCategory.map((eintrag) => (
													<TableRow key={eintrag._id}>
														{kategorie.felder
															.filter((feld) => !feld.istExtrafeld)
															.map((feld) => {
																const feldWertObj = eintrag.feldwerte.find(
																	(fw) => fw.feldId === feld.feldId,
																);
																const displayValue =
																	feldWertObj?.feldWert || "-";
																const fieldType =
																	feld.typ as keyof typeof FIELD_TYPES;
																const isHiddenVisible =
																	hiddenFieldsVisibility[kategorie._id];
																const pwdCopyId = `pwd-${eintrag._id}-${feld.feldId}`;

																return (
																	<TableCell
																		key={feld.feldId}
																		className="align-top py-1.5 px-3 text-xs"
																	>
																		{fieldType === FIELD_TYPES.PASSWORD ||
																		feld.istVersteckt ? (
																			<div className="flex items-center gap-1">
																				<span className="italic text-gray-500">
																					{isHiddenVisible
																						? displayValue
																						: "********"}
																				</span>
																				<Button
																					variant="ghost"
																					size="icon"
																					onClick={() =>
																						copyToClipboard(
																							displayValue,
																							pwdCopyId,
																							true,
																						)
																					}
																					className={`h-6 w-6 ${passwordCopiedStates[pwdCopyId] ? "text-green-400" : "text-blue-400 hover:text-blue-500"} hover:bg-blue-500/10 shrink-0`}
																					title="Wert kopieren"
																				>
																					<Copy className="h-3 w-3" />
																				</Button>
																			</div>
																		) : fieldType === FIELD_TYPES.URL ? (
																			<div className="flex items-center gap-1">
																				<a
																					href={
																						displayValue.startsWith("http")
																							? displayValue
																							: `https://${displayValue}`
																					}
																					target="_blank"
																					rel="noopener noreferrer"
																					className="text-cyan-400 hover:text-cyan-300 break-all"
																				>
																					{displayValue}
																				</a>
																				{feld.istKopierbar &&
																					displayValue !== "-" && (
																						<Button
																							variant="ghost"
																							size="icon"
																							onClick={() =>
																								copyToClipboard(
																									displayValue,
																									`field-${eintrag._id}-${feld.feldId}`,
																								)
																							}
																							className={`h-6 w-6 ${copiedStates[`field-${eintrag._id}-${feld.feldId}`] ? "text-green-400" : "text-blue-400 hover:text-blue-500"} hover:bg-blue-500/10 shrink-0`}
																							title="Wert kopieren"
																						>
																							<Copy className="h-3 w-3" />
																						</Button>
																					)}
																			</div>
																		) : fieldType === FIELD_TYPES.EMAIL ? (
																			<div className="flex items-center gap-1">
																				<a
																					href={`mailto:${displayValue}`}
																					className="text-cyan-400 hover:text-cyan-300 break-all"
																				>
																					{displayValue}
																				</a>
																				{feld.istKopierbar &&
																					displayValue !== "-" && (
																						<Button
																							variant="ghost"
																							size="icon"
																							onClick={() =>
																								copyToClipboard(
																									displayValue,
																									`field-${eintrag._id}-${feld.feldId}`,
																								)
																							}
																							className={`h-6 w-6 ${copiedStates[`field-${eintrag._id}-${feld.feldId}`] ? "text-green-400" : "text-blue-400 hover:text-blue-500"} hover:bg-blue-500/10 shrink-0`}
																							title="Wert kopieren"
																						>
																							<Copy className="h-3 w-3" />
																						</Button>
																					)}
																			</div>
																		) : fieldType === FIELD_TYPES.TEXTAREA ? (
																			<div className="flex items-start gap-1">
																				<pre className="whitespace-pre-wrap text-xs font-sans break-words">
																					{displayValue}
																				</pre>
																				{feld.istKopierbar &&
																					displayValue !== "-" && (
																						<Button
																							variant="ghost"
																							size="icon"
																							onClick={() =>
																								copyToClipboard(
																									displayValue,
																									`field-${eintrag._id}-${feld.feldId}`,
																								)
																							}
																							className={`h-6 w-6 ${copiedStates[`field-${eintrag._id}-${feld.feldId}`] ? "text-green-400" : "text-blue-400 hover:text-blue-500"} hover:bg-blue-500/10 shrink-0`}
																							title="Wert kopieren"
																						>
																							<Copy className="h-3 w-3" />
																						</Button>
																					)}
																			</div>
																		) : (
																			<div className="flex items-center gap-1">
																				<span className="break-words">
																					{displayValue}
																				</span>
																				{feld.istKopierbar &&
																					displayValue !== "-" && (
																						<Button
																							variant="ghost"
																							size="icon"
																							onClick={() =>
																								copyToClipboard(
																									displayValue,
																									`field-${eintrag._id}-${feld.feldId}`,
																								)
																							}
																							className={`h-6 w-6 ${copiedStates[`field-${eintrag._id}-${feld.feldId}`] ? "text-green-400" : "text-blue-400 hover:text-blue-500"} hover:bg-blue-500/10 shrink-0`}
																							title="Wert kopieren"
																						>
																							<Copy className="h-3 w-3" />
																						</Button>
																					)}
																			</div>
																		)}
																	</TableCell>
																);
															})}

														<TableCell className="text-center align-middle py-1.5 px-3 text-xs">
															<div className="flex justify-center gap-1">
																{/* Extrafelder-Button, nur anzeigen wenn es Extrafelder gibt */}
																{kategorie.felder.some(
																	(feld) => feld.istExtrafeld,
																) &&
																	eintrag.feldwerte.some((fw) => {
																		const feld = kategorie.felder.find(
																			(f) => f.feldId === fw.feldId,
																		);
																		return (
																			feld?.istExtrafeld &&
																			fw.feldWert &&
																			fw.feldWert.trim() !== ""
																		);
																	}) && (
																		<Dialog>
																			<DialogTrigger asChild>
																				<Button
																					variant="ghost"
																					size="icon"
																					className="h-6 w-6 text-purple-400 hover:text-purple-500 hover:bg-purple-500/10"
																					title="Extrafelder anzeigen"
																				>
																					<NotebookText className="h-3 w-3" />
																				</Button>
																			</DialogTrigger>
																			<DialogContent className="sm:max-w-[600px]">
																				<DialogHeader>
																					<DialogTitle>Extrafelder</DialogTitle>
																					<DialogDescription id="extrafelder-description">
																						Zusätzliche Informationen zu diesem
																						Eintrag
																					</DialogDescription>
																				</DialogHeader>
																				<div className="flex flex-col gap-4 mt-4 text-xs">
																					{kategorie.felder
																						.filter((feld) => feld.istExtrafeld)
																						.map((feld) => {
																							const feldWertObj =
																								eintrag.feldwerte.find(
																									(fw) =>
																										fw.feldId === feld.feldId,
																								);
																							const displayValue =
																								feldWertObj?.feldWert || "-";
																							const fieldType =
																								feld.typ as keyof typeof FIELD_TYPES;
																							const copyId = `extrafield-${eintrag._id}-${feld.feldId}`;
																							const showCopyButton =
																								feld.istKopierbar &&
																								displayValue !== "-";

																							return (
																								<div
																									key={feld.feldId}
																									className="flex flex-col border-b border-gray-700 pb-3 last:border-0 last:pb-0"
																								>
																									<span className="text-xs font-medium text-gray-300 mb-1">
																										{feld.name}
																										{feld.istErforderlich && (
																											<span className="text-red-400 ml-0.5">
																												*
																											</span>
																										)}
																									</span>
																									<div className="flex items-start gap-1.5">
																										{fieldType ===
																										FIELD_TYPES.TEXTAREA ? (
																											<pre className="whitespace-pre-wrap text-xs font-sans break-words bg-gray-800/50 p-2 rounded flex-1">
																												{displayValue}
																											</pre>
																										) : (
																											<span className="break-words text-gray-200 text-xs pl-0.5">
																												{displayValue}
																											</span>
																										)}
																										{showCopyButton && (
																											<Button
																												variant="ghost"
																												size="icon"
																												onClick={() =>
																													copyToClipboard(
																														displayValue,
																														copyId,
																													)
																												}
																												className={`h-6 w-6 ${copiedStates[copyId] ? "text-green-400" : "text-blue-400 hover:text-blue-500"} hover:bg-blue-500/10 shrink-0`}
																												title="Wert kopieren"
																											>
																												<Copy className="h-3 w-3" />
																											</Button>
																										)}
																									</div>
																								</div>
																							);
																						})}
																				</div>
																				<DialogFooter className="mt-4">
																					<DialogClose asChild>
																						<Button variant="outline">
																							Schließen
																						</Button>
																					</DialogClose>
																				</DialogFooter>
																			</DialogContent>
																		</Dialog>
																	)}
																<Button
																	variant="ghost"
																	size="icon"
																	onClick={() =>
																		handleEditEntry(kategorie._id, eintrag._id)
																	}
																	className="h-6 w-6 text-blue-400 hover:text-blue-500 hover:bg-blue-500/10"
																	title="Eintrag bearbeiten"
																>
																	<Pencil className="h-3 w-3" />
																</Button>
																<Button
																	variant="ghost"
																	size="icon"
																	onClick={() => handleDeleteEntry(eintrag._id)}
																	className="h-6 w-6 text-red-400 hover:text-red-500 hover:bg-red-500/10"
																	title="Eintrag löschen"
																>
																	<Trash2 className="h-3 w-3" />
																</Button>
															</div>
														</TableCell>
													</TableRow>
												))}
											</TableBody>
										</Table>
									)}
								</CardContent>
							)}
						</Card>
					);
				})}
				{sortedKategorien.length === 0 && !isInitializingDefaults && (
					<Card className="shadow-lg border-0">
						<CardContent className="p-6 text-center text-gray-400 text-xs">
							Keine Dokumentationskategorien gefunden. Bitte legen Sie welche
							unter "System {">"} Doku-Kategorien" an oder synchronisieren Sie
							die Standardkategorien.
						</CardContent>
					</Card>
				)}
			</div>
		</PageLayout>
	);
}

export default KundenDokuDetailPage;
