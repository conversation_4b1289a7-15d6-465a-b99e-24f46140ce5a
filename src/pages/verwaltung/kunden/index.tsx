import { api } from "@/../convex/_generated/api";
import type { Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import { KundenDataTable } from "@/components/verwaltung/kunden/KundenDataTable";
import { KundenFilterControls } from "@/components/verwaltung/kunden/KundenFilterControls";
import {
	AnsprechpartnerData,
	KundeFormData,
	KundenForm,
	StandortData,
} from "@/components/verwaltung/kunden/KundenForm"; // Import new types
import { EmptyState } from "@/components/layout/EmptyState"; // Import new EmptyState
import { PageLayout } from "@/components/layout/PageLayout";
import { StandardDataTable } from "@/components/layout/StandardDataTable";
import { formatCurrency } from "@/lib/utils/formatUtils";
import { useMutation, useQuery } from "convex/react";
import { Briefcase, PlusCircle, X } from "lucide-react";
import { useMemo, useState } from "react";
import { Link } from "react-router-dom";
import { toast } from "sonner";

// Interface für Kundendaten, um die neuen Felder von `api.verwaltung.kunden.list` zu reflektieren
// This interface should match the return type of `api.verwaltung.kunden.list`
export interface Kunde {
	_id: Id<"kunden">;
	_creationTime: number;
	name: string;
	stundenpreis: number;
	anfahrtskosten: number;
	standorte: StandortData[]; // Array of StandortData
	ansprechpartner: AnsprechpartnerData[]; // Array of AnsprechpartnerData
}

// Export types for use by KundenDataTable
export type { StandortData, AnsprechpartnerData };

export function KundenPage() {
	const kunden: Kunde[] = useQuery(api.verwaltung.kunden.list) || [];
	const removeKunde = useMutation(api.verwaltung.kunden.remove);

	const [editingId, setEditingId] = useState<Id<"kunden"> | null>(null);
	const [showForm, setShowForm] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");

	const editingKunde = useMemo((): (Kunde & { _id: Id<"kunden"> }) | null => {
		if (!editingId) return null;
		const kunde = kunden.find((k) => k._id === editingId);
		if (!kunde) return null;
		// Ensure that all array fields are present, even if empty
		return {
			...kunde,
			standorte: kunde.standorte || [],
			ansprechpartner: kunde.ansprechpartner || [],
		};
	}, [editingId, kunden]);

	const handleFormSubmitSuccess = () => {
		setShowForm(false);
		setEditingId(null);
	};

	const handleFormCancel = () => {
		setShowForm(false);
		setEditingId(null);
	};

	const handleEdit = (kunde: Kunde) => {
		setEditingId(kunde._id);
		setShowForm(true);
	};

	const handleDelete = async (id: Id<"kunden">) => {
		if (
			window.confirm(
				"Möchten Sie diesen Kunden wirklich löschen? Verknüpfte Leistungen oder Kontingente verhindern das Löschen.",
			)
		) {
			try {
				await removeKunde({ id });
				toast.success("Kunde erfolgreich gelöscht");
				if (id === editingId) {
					handleFormCancel();
				}
			} catch (error) {
				toast.error(
					`Fehler beim Löschen: ${String((error as Error).message || error)}`,
				);
			}
		}
	};

	const getHauptstandortOrt = (kunde: Kunde): string => {
		const hauptstandort = kunde.standorte?.find((s) => s.istHauptstandort);
		return hauptstandort?.ort ?? kunde.standorte?.[0]?.ort ?? "-";
	};

	const getHauptansprechpartnerInfo = (
		kunde: Kunde,
	): { email?: string; name?: string } => {
		const hauptansprechpartner = kunde.ansprechpartner?.find(
			(a) => a.istHauptansprechpartner,
		);
		if (hauptansprechpartner)
			return {
				email: hauptansprechpartner.email,
				name: hauptansprechpartner.name,
			};

		const ersterAnsprechpartner = kunde.ansprechpartner?.[0];
		if (ersterAnsprechpartner)
			return {
				email: ersterAnsprechpartner.email,
				name: ersterAnsprechpartner.name,
			};

		return { email: "-", name: "-" };
	};

	const filteredKunden = useMemo(
		() =>
			kunden.filter((kunde) => {
				const lowerSearchTerm = searchTerm.toLowerCase();
				const nameMatch = kunde.name.toLowerCase().includes(lowerSearchTerm);
				const ortMatch = kunde.standorte?.some((s) =>
					s.ort.toLowerCase().includes(lowerSearchTerm),
				);
				const ansprechpartnerNameMatch = kunde.ansprechpartner?.some((a) =>
					a.name.toLowerCase().includes(lowerSearchTerm),
				);
				const ansprechpartnerEmailMatch = kunde.ansprechpartner?.some((a) =>
					a.email?.toLowerCase().includes(lowerSearchTerm),
				);

				return (
					nameMatch ||
					ortMatch ||
					ansprechpartnerNameMatch ||
					ansprechpartnerEmailMatch
				);
			}),
		[kunden, searchTerm],
	);

	const handleSearchTermChange = (value: string) => {
		setSearchTerm(value);
	};

	const resetFilters = () => {
		setSearchTerm("");
	};

	const actionButton = showForm ? (
		<Button onClick={handleFormCancel} size="sm" className="gap-1">
			<X className="h-4 w-4" /> Abbrechen
		</Button>
	) : (
		<Link to="/verwaltung/kunden/neu">
			<Button size="sm" className="gap-1">
				<PlusCircle className="h-4 w-4" /> Neuer Kunde
			</Button>
		</Link>
	);

	return (
		<PageLayout
			title="Kunden"
			subtitle="Verwalten Sie hier Ihre Kundendaten und zugehörige Informationen"
			action={actionButton}
		>
			{showForm && (
				<KundenForm
					editingKunde={editingKunde} // This will pass the full Kunde object or null
					onSubmitSuccess={handleFormSubmitSuccess}
					onCancel={handleFormCancel}
				/>
			)}

			<StandardDataTable
				title="Kundenübersicht"
				infoSlot={
					<>
						<Briefcase className="h-3.5 w-3.5 opacity-70" />
						<span>
							{filteredKunden.length}{" "}
							{filteredKunden.length === 1 ? "Kunde" : "Kunden"}
						</span>
					</>
				}
				filterSlot={
					<KundenFilterControls
						searchTerm={searchTerm}
						onSearchTermChange={handleSearchTermChange}
					/>
				}
			>
				{filteredKunden.length === 0 && !showForm ? (
					<EmptyState
						icon={<Briefcase className="w-12 h-12" />} // Adjusted icon size
						title="Keine Kunden gefunden"
						message={
							searchTerm
								? "Versuchen Sie, Ihre Suche anzupassen"
								: "Erstellen Sie einen neuen Kunden mit dem Button oben rechts"
						}
						actions={
							searchTerm && (
								<Button variant="outline" size="sm" onClick={resetFilters}>
									Filter zurücksetzen
								</Button>
							)
						}
					/>
				) : (
					<KundenDataTable
						kunden={filteredKunden}
						onEdit={handleEdit}
						onDelete={handleDelete}
						formatCurrency={formatCurrency}
						getHauptstandortOrt={getHauptstandortOrt}
						getHauptansprechpartnerInfo={getHauptansprechpartnerInfo}
					/>
				)}
			</StandardDataTable>
		</PageLayout>
	);
}

export default KundenPage;
