/**
 * Formatiert einen Zahlenwert als Währung (EUR).
 */
export function formatCurrency(amount: number | null | undefined): string {
	if (amount === null || amount === undefined || Number.isNaN(amount)) {
		return "-";
	}
	return new Intl.NumberFormat("de-DE", {
		style: "currency",
		currency: "EUR",
	}).format(amount);
}

/**
 * Formatiert Stunden auf Viertelstunden gerundet und als String mit " h".
 * Beispiel: 2.75 -> "2.75 h"
 */
export function formatHours(hours: number | null | undefined): string {
	if (hours === null || hours === undefined || Number.isNaN(hours)) {
		return "-";
	}
	const rounded = Math.round(hours * 4) / 4;
	return `${rounded.toFixed(2)}h`;
}

/**
 * Formatiert einen Timestamp oder ein Date-Objekt als Datum im deutschen Format (DD.MM.YYYY).
 */
export function formatDate(
	dateValue: number | Date | null | undefined,
): string {
	if (dateValue === null || dateValue === undefined) {
		return "-";
	}
	try {
		return new Date(dateValue).toLocaleDateString("de-DE", {
			timeZone: "Europe/Berlin",
		});
	} catch (e) {
		return "-";
	}
}

/**
 * Formatiert einen Timestamp oder ein Date-Objekt als Uhrzeit im deutschen Format (HH:MM).
 */
export function formatTime(
	dateValue: number | Date | null | undefined,
): string {
	if (dateValue === null || dateValue === undefined) {
		return "-";
	}
	try {
		const date = new Date(dateValue);
		return date.toLocaleTimeString("de-DE", {
			hour: "2-digit",
			minute: "2-digit",
			timeZone: "Europe/Berlin",
			hour12: false,
		});
	} catch (e) {
		return "-";
	}
}

/**
 * Konvertiert einen Timestamp in ein für Datums-Input-Felder geeignetes Format (YYYY-MM-DD).
 */
export function toInputDate(timestamp: number | null | undefined): string {
	if (timestamp === null || timestamp === undefined) return "";
	try {
		const date = new Date(timestamp);
		const formatter = new Intl.DateTimeFormat("en-CA", {
			year: "numeric",
			month: "2-digit",
			day: "2-digit",
			timeZone: "Europe/Berlin",
		});
		return formatter.format(date);
	} catch (e) {
		return "";
	}
}

/**
 * Konvertiert einen Timestamp in ein für Zeit-Input-Felder geeignetes Format (HH:MM).
 */
export function toInputTime(timestamp: number | null | undefined): string {
	if (timestamp === null || timestamp === undefined) return "";
	try {
		const date = new Date(timestamp);
		return date.toLocaleTimeString("de-DE", {
			hour: "2-digit",
			minute: "2-digit",
			timeZone: "Europe/Berlin",
			hour12: false,
		});
	} catch (e) {
		return "";
	}
}

/**
 * Calculates the duration in hours between two timestamps, rounded up to the nearest quarter hour.
 * @param start The start timestamp.
 * @param end The end timestamp.
 * @returns The duration in hours.
 */
export function calculateHours(start: number, end: number): number {
	if (end <= start) return 0;
	const diffInMinutes = (end - start) / (1000 * 60);
	return Math.ceil(diffInMinutes / 15) / 4;
}
